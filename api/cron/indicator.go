package cron

import (
	"context"
	"sync"

	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

type IndicatorCacheTask struct {
	IndicatorService       *servicev2.IndicatorService
	DataSourceTableService *servicev2.DataSourceTableService
}

func NewIndicatorCacheTask() *IndicatorCacheTask {
	return &IndicatorCacheTask{
		DataSourceTableService: &servicev2.DataSourceTableService{
			DataSourceTableRepo: datav2.NewRmsDataSourceTable(),
			DataSourceRepo:      datav2.NewRmsDataSource(),
		},
		IndicatorService: &servicev2.IndicatorService{
			IndicatorRepo:        datav2.NewRmsIndicator(),
			IndicatorVersionRepo: datav2.NewRmsIndicatorVersion(),
			IndicatorRuleRepo:    datav2.NewRmsIndicatorRule(),
			IndicatorMeasureRepo: datav2.NewRmsIndicatorMeasure(),
			IndicatorHistoryRepo: datav2.NewRmsIndicatorVersionHistory(),
			DataSourceTableRepo:  datav2.NewRmsDataSourceTable(),
			DataSourceRepo:       datav2.NewRmsDataSource(),
		},
	}
}
func (c *IndicatorCacheTask) Init() (err error) {
	initFn := func() (innerErr error) {
		ctx := context.Background()
		var tableIDToType = make(map[int64]string)
		tableIDToType, err = c.DataSourceTableService.GetTableToSourceType(ctx)
		if err != nil {
			return
		}
		// 获取指标配置列表
		var indicators []*modelv2.RmsIndicator
		_, indicators, innerErr = c.IndicatorService.IndicatorRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("is_script = ?", false).Where("status != ?", modelv2.IndicatorStatusDisable).Where("current_version_id > ?", 0)
		}, false)
		if innerErr != nil {
			return
		}
		var lock sync.Mutex
		var preIndicators = make(map[string][]*entity.Indicator)
		var eg errgroup.Group
		for _, indicator := range indicators {
			tmp := indicator
			eg.Go(func() (e error) {
				var preData *entity.Indicator
				preData, e = c.IndicatorService.GetIndicatorSqlData(ctx, tmp.ID, tmp.CurrentVersionID)
				if e != nil {
					return
				}
				defer func() {
					lock.Unlock()
				}()
				lock.Lock()
				preIndicators[preData.AccessPoint] = append(preIndicators[preData.AccessPoint], preData)
				return
			})
		}
		if err = eg.Wait(); err != nil {
			return
		}
		var visualSqlCache = make(map[string]map[string]string)
		var scriptSqlCache = make(map[string]map[string]string)
		for _, accessPoint := range preIndicators {
			for _, indicator := range accessPoint {
				var sql string
				var placeholder []string
				switch tableIDToType[indicator.TableID] {
				case "mysql":
					sql, placeholder, err = c.IndicatorService.Indicator2MysqlSql(indicator)
				case "doris":
					sql, placeholder, err = c.IndicatorService.Indicator2DorisSql(indicator)
				}
				if sql != "" {
					if visualSqlCache[indicator.AccessPoint] == nil {
						visualSqlCache[indicator.AccessPoint] = make(map[string]string)
					}
					jsonData, _ := jsoniter.MarshalToString(map[string]interface{}{
						"code":        sql,
						"placeholder": placeholder,
					})
					visualSqlCache[indicator.AccessPoint][indicator.IndicatorName] = jsonData
				}
			}
		}
		// 生成Script指标缓存数据
		_, indicators, innerErr = c.IndicatorService.IndicatorRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("is_script = ?", true).Where("status != ?", modelv2.IndicatorStatusDisable).Where("current_version_id > ?", 0)
		}, false)
		if innerErr != nil {
			return
		}
		var versionIDs []int64
		var indicatorIDToIndicator = make(map[int64]*modelv2.RmsIndicator)
		for _, indicator := range indicators {
			if scriptSqlCache[indicator.AccessPoint] == nil {
				scriptSqlCache[indicator.AccessPoint] = make(map[string]string)
			}
			indicatorIDToIndicator[indicator.ID] = indicator
			versionIDs = append(versionIDs, indicator.CurrentVersionID)
		}
		var versions []*modelv2.RmsIndicatorVersion
		_, versions, innerErr = c.IndicatorService.IndicatorVersionRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("id in ?", versionIDs)
		}, false)
		if innerErr != nil {
			return
		}
		for _, version := range versions {
			ind := indicatorIDToIndicator[version.IndicatorID]
			jsonData, _ := jsoniter.MarshalToString(map[string]interface{}{
				"code":        version.Script,
				"placeholder": "",
			})
			scriptSqlCache[ind.AccessPoint][ind.IndicatorName] = jsonData

		}

		return FlushIndicator(visualSqlCache, scriptSqlCache)
	}
	err = initFn()
	if err != nil {
		return
	}
	//_, err = CronTasker.AddFunc("0 0 0 * * *", func() {
	//	innerErr := initFn()
	//	if innerErr != nil {
	//		wlog.Error("Indicator Cache flush", zap.Any("innerErr", innerErr))
	//	}
	//})
	return
}

func FlushIndicator(visualSqlCache, scriptSqlCache map[string]map[string]string) (err error) {
	var visualIndicatorExists, scriptIndicatorExists []string
	var visualAccessPointMap = make(map[string]bool)
	var scriptAccessPointMap = make(map[string]bool)
	visualIndicatorExists, err = redisutils.GetPrefixKeys(modelv2.RedisCacheVisualIndicator)
	if err != nil {
		return
	}
	scriptIndicatorExists, err = redisutils.GetPrefixKeys(modelv2.RedisCacheScriptIndicator)
	if err != nil {
		return
	}
	for _, exist := range visualIndicatorExists {
		visualAccessPointMap[exist] = true
	}
	for _, exist := range scriptIndicatorExists {
		scriptAccessPointMap[exist] = true
	}

	_, err = redisutils.Pipeline(func(rdb redis.Pipeliner) (innerErr error) {
		ctx := context.Background()
		innerErr = rdb.Del(ctx, modelv2.RedisCacheVisualIndicator).Err()
		if innerErr != nil {
			innerErr = er.Internal.WithMsg("failed to flush indicator cache[1]").WithErr(innerErr).WithStack()
			return
		}
		for accessPoint, indicators := range visualSqlCache {
			var key = modelv2.RedisCacheVisualIndicator + ":" + accessPoint
			if visualAccessPointMap[key] {
				innerErr = rdb.Del(ctx, key).Err()
				if innerErr != nil {
					innerErr = er.Internal.WithMsg("failed to flush indicator cache[1]").WithErr(innerErr).WithStack()
					return
				}
				delete(visualAccessPointMap, key)
			}
			innerErr = rdb.HMSet(ctx, modelv2.RedisCacheVisualIndicator+":"+accessPoint, indicators).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[2]").WithErr(innerErr).WithStack()
				return
			}
		}
		innerErr = rdb.Del(ctx, modelv2.RedisCacheScriptIndicator).Err()
		if innerErr != nil {
			innerErr = er.Internal.WithMsg("failed to flush indicator cache[3]").WithErr(innerErr).WithStack()
			return
		}
		for accessPoint, indicators := range scriptSqlCache {
			var key = modelv2.RedisCacheScriptIndicator + ":" + accessPoint
			if scriptAccessPointMap[key] {
				innerErr = rdb.Del(ctx, key).Err()
				if innerErr != nil {
					innerErr = er.Internal.WithMsg("failed to flush indicator cache[1]").WithErr(innerErr).WithStack()
					return
				}
				delete(scriptAccessPointMap, key)
			}
			innerErr = rdb.HMSet(ctx, key, indicators).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[4]").WithErr(innerErr).WithStack()
				return
			}
		}
		for accessPoint, _ := range scriptAccessPointMap {
			innerErr = rdb.Del(ctx, modelv2.RedisCacheScriptIndicator+":"+accessPoint).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[7]").WithErr(innerErr).WithStack()
				return
			}
		}
		for accessPoint, _ := range visualAccessPointMap {
			innerErr = rdb.Del(ctx, modelv2.RedisCacheVisualIndicator+":"+accessPoint).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[8]").WithErr(innerErr).WithStack()
				return
			}
		}
		return nil
	})
	return
}
