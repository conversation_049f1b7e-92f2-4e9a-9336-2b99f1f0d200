// Code generated by Wire.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package pro

import (
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/cron"
	handlev1 "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	handlev2 "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	v12 "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/routers/v1"
	v2 "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/routers/v2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	//"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/jwt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/logger"
)

// Injectors from wire.go:

// wireApp init
func wireApp() (func() error, func(), error) {

	// 项目终止时，定时任务也一并终止
	//myCtx, cancel := context.WithCancel(context.Background())
	var err error
	cfg := config.GetConfig()

	zapLogger, err := logger.NewLogger(cfg)

	if err != nil {
		return nil, nil, err
	}
	global.Logger = zapLogger
	baseHandle := &handlev1.BaseHandle{
		Log: zapLogger,
	}
	if global.DB, err = db.InitDB(cfg.Mysql); err != nil {
		fmt.Printf("db init failed, error:%v\n", err)
		panic(err)
	}

	if err := db.InitDoris(cfg.Doris); err != nil {
		fmt.Printf("Doris init failed, error:%v\n", err)
		panic(err)
	}
	global.Ddb = db.Doris()
	if cfg.App.Mode == "local" {
		modelv2.InitModel() //代码整理后迁移
	}
	if global.Rdb, err = db.InitRedis(cfg.Redis); err != nil {

		fmt.Printf("Rdb init failed, error:%v\n", err)
		panic(err)
	}
	global.CraParamter = cfg.CraParamter
	global.CraParamterMap = make(map[string]string, 0)
	for _, s := range global.CraParamter.Company {
		global.CraParamterMap[s.DataField] = s.FormField

	}
	for _, s := range global.CraParamter.Individual {
		global.CraParamterMap[s.DataField] = s.FormField

	}
	if err != nil {
		return nil, nil, err
	}
	base := data.NewBase(global.DB)

	Dbase := data.NewBase(db.Doris())
	alarmContact := &data.AlarmContact{
		Base: base,
	}
	alarmContactService := &service.AlarmContactService{
		Logger:           zapLogger,
		AlarmContactRepo: alarmContact,
	}
	alarmContactHandle := &handlev1.AlarmContactHandle{
		BaseHandle:          baseHandle,
		AlarmContactService: alarmContactService,
	}
	alarmContactRouter := &v12.AlarmContactRouter{
		AlarmContactHandle: alarmContactHandle,
	}
	alarmTimeConf := &data.AlarmTimeConf{
		Base: base,
	}
	alarmTimeConfService := &service.AlarmTimeConfService{
		Logger:            zapLogger,
		AlarmTimeConfRepo: alarmTimeConf,
	}
	alarmTimeConfHandle := &handlev1.AlarmTimeConfHandle{
		BaseHandle:           baseHandle,
		AlarmTimeConfService: alarmTimeConfService,
	}
	alarmTimeConfRouter := &v12.AlarmTimeConfRouter{
		AlarmTimeConfHandle: alarmTimeConfHandle,
	}
	blacklist := &data.Blacklist{
		Base: base,
	}
	blacklistService := &service.BlacklistService{
		Logger:        zapLogger,
		BlacklistRepo: blacklist,
	}
	blacklistHandle := &handlev1.BlacklistHandle{
		BaseHandle:       baseHandle,
		BlacklistService: blacklistService,
	}
	blacklistRouter := &v12.BlacklistRouter{
		BlacklistHandle: blacklistHandle,
	}
	businessType := &data.BusinessType{
		Base: base,
	}
	checkPoint := &data.CheckPoint{
		Base: base,
	}
	businessTypeService := &service.BusinessTypeService{
		Logger:           zapLogger,
		BusinessTypeRepo: businessType,
		CheckPointRepo:   checkPoint,
	}
	businessTypeHandle := &handlev1.BusinessTypeHandle{
		BaseHandle:          baseHandle,
		BusinessTypeService: businessTypeService,
	}
	businessTypeRouter := &v12.BusinessTypeRouter{
		BusinessTypeHandle: businessTypeHandle,
	}
	configProgress := &data.ConfigProgress{
		Base: base,
	}
	filterField := &data.FilterField{
		Base: base,
	}
	baseServer := service.BaseServer{}
	checkPointService := &service.CheckPointService{
		Logger:         zapLogger,
		CheckPointRepo: checkPoint,
		ConfigProgress: configProgress,
		BusinessType:   businessType,
		FilterField:    filterField,
		BaseServer:     baseServer,
	}
	checkPointHandle := &handlev1.CheckPointHandle{
		BaseHandle:        baseHandle,
		CheckPointService: checkPointService,
	}
	checkPointRouter := &v12.CheckPointRouter{
		CheckPointHandle: checkPointHandle,
	}
	event := &data.Event{
		Base: base,
	}
	eventService := &service.EventService{
		Logger:    zapLogger,
		EventRepo: event,
	}
	eventHandle := &handlev1.EventHandle{
		BaseHandle:   baseHandle,
		EventService: eventService,
	}
	eventRouter := &v12.EventRouter{
		EventHandle: eventHandle,
	}
	eventField := &data.EventField{
		Base: base,
	}
	policy := &data.Policy{
		Base: base,
	}
	eventStoreCfg := &data.EventStoreCfg{
		Base: base,
	}
	eventFieldService := &service.EventFieldService{
		BaseServer:            baseServer,
		Logger:                zapLogger,
		EventFieldRepo:        eventField,
		CheckPoint:            checkPoint,
		FilterField:           filterField,
		PolicyRepo:            policy,
		EventFieldBindingRepo: datav2.NewRmsEventFieldBinding(),
		EventStoreCfgRepo:     eventStoreCfg,
	}
	eventFieldHandle := &handlev1.EventFieldHandle{
		BaseHandle:        baseHandle,
		EventFieldService: eventFieldService,
	}
	eventFieldRouter := &v12.EventFieldRouter{
		EventFieldHandle: eventFieldHandle,
	}

	eventStoreCfgService := &service.EventStoreCfgService{
		Logger:            zapLogger,
		EventStoreCfgRepo: eventStoreCfg,
	}
	eventStoreCfgHandle := &handlev1.EventStoreCfgHandle{
		BaseHandle:           baseHandle,
		EventStoreCfgService: eventStoreCfgService,
	}
	eventStoreCfgRouter := &v12.EventStoreCfgRouter{
		EventStoreCfgHandle: eventStoreCfgHandle,
	}
	filterFieldService := &service.FilterFieldService{
		Logger:          zapLogger,
		FilterFieldRepo: filterField,
		PolicyRepo:      policy,
		CheckPointRepo:  checkPoint,
	}
	filterFieldHandle := &handlev1.FilterFieldHandle{
		BaseHandle:         baseHandle,
		FilterFieldService: filterFieldService,
	}
	filterFieldRouter := &v12.FilterFieldRouter{
		FilterFieldHandle: filterFieldHandle,
	}
	operatorLog := &data.OperatorLog{
		Base: base,
	}
	operatorLogService := &service.OperatorLogService{
		Logger:          zapLogger,
		OperatorLogRepo: operatorLog,
	}
	operatorLogHandle := &handlev1.OperatorLogHandle{
		BaseHandle:         baseHandle,
		OperatorLogService: operatorLogService,
	}
	operatorLogRouter := &v12.OperatorLogRouter{
		OperatorLogHandle: operatorLogHandle,
	}
	policyService := &service.PolicyService{
		Logger:     zapLogger,
		PolicyRepo: policy,
		BaseServer: baseServer,
	}
	policyHandle := &handlev1.PolicyHandle{
		BaseHandle:    baseHandle,
		PolicyService: policyService,
	}
	policyRouter := &v12.PolicyRouter{
		PolicyHandle: policyHandle,
	}
	riskLog := &data.RiskLog{
		Base: base,
	}
	riskLogService := &service.RiskLogService{
		Logger:      zapLogger,
		RiskLogRepo: riskLog,
	}
	riskLogHandle := &handlev1.RiskLogHandle{
		BaseHandle:     baseHandle,
		RiskLogService: riskLogService,
	}
	riskLogRouter := &v12.RiskLogRouter{
		RiskLogHandle: riskLogHandle,
	}
	rule := &data.Rule{
		Base: base,
	}
	ruleService := &service.RuleService{
		Logger:     zapLogger,
		RuleRepo:   rule,
		Conf:       cfg,
		BaseServer: baseServer,
	}
	ruleHandle := &handlev1.RuleHandle{
		BaseHandle:  baseHandle,
		RuleService: ruleService,
	}
	ruleRouter := &v12.RuleRouter{
		RuleHandle: ruleHandle,
	}
	ruleParameter := &data.RuleParameter{
		Base: base,
	}
	ruleParameterService := &service.RuleParameterService{
		Logger:            zapLogger,
		RuleParameterRepo: ruleParameter,
	}
	ruleParameterHandle := &handlev1.RuleParameterHandle{
		BaseHandle:           baseHandle,
		RuleParameterService: ruleParameterService,
	}
	ruleParameterRouter := &v12.RuleParameterRouter{
		RuleParameterHandle: ruleParameterHandle,
	}
	ruleParameterGroup := &data.RuleParameterGroup{
		Base: base,
	}
	ruleParameterGroupBind := &data.RuleParameterGroupBind{
		Base: base,
	}
	ruleParameterGroupService := &service.RuleParameterGroupService{
		Logger:                     zapLogger,
		RuleParameterGroupRepo:     ruleParameterGroup,
		RuleParameterGroupBindRepo: ruleParameterGroupBind,
	}
	ruleParameterGroupHandle := &handlev1.RuleParameterGroupHandle{
		BaseHandle:                baseHandle,
		RuleParameterGroupService: ruleParameterGroupService,
	}
	ruleParameterGroupRouter := &v12.RuleParameterGroupRouter{
		RuleParameterGroupHandle: ruleParameterGroupHandle,
	}
	ruleParameterGroupBindService := &service.RuleParameterGroupBindService{
		Logger:                     zapLogger,
		RuleParameterGroupBindRepo: ruleParameterGroupBind,
		RuleParameterGroup:         ruleParameterGroup,
		BaseServer:                 baseServer,
	}
	ruleParameterGroupBindHandle := &handlev1.RuleParameterGroupBindHandle{
		BaseHandle:                    baseHandle,
		RuleParameterGroupBindService: ruleParameterGroupBindService,
	}
	ruleParameterGroupBindRouter := &v12.RuleParameterGroupBindRouter{
		RuleParameterGroupBindHandle: ruleParameterGroupBindHandle,
	}
	ruleParameterValue := &data.RuleParameterValue{
		Base: base,
	}
	ruleParameterValueService := &service.RuleParameterValueService{
		Logger:                 zapLogger,
		RuleParameterValueRepo: ruleParameterValue,
		RuleParameterGroup:     ruleParameterGroup,
		RuleParameter:          ruleParameter,
	}
	ruleParameterValueHandle := &handlev1.RuleParameterValueHandle{
		BaseHandle:                baseHandle,
		RuleParameterValueService: ruleParameterValueService,
	}
	ruleParameterValueRouter := &v12.RuleParameterValueRouter{
		RuleParameterValueHandle: ruleParameterValueHandle,
	}
	handleLog := &data.HandleLog{
		Base: base,
	}
	handleLogService := &service.HandleLogService{
		Logger:        zapLogger,
		HandleLogRepo: handleLog,
	}
	handleLogHandle := &handlev1.HandleLogHandle{
		BaseHandle:       baseHandle,
		HandleLogService: handleLogService,
	}
	handleLogRouter := &v12.HandleLogRouter{
		HandleLogHandle: handleLogHandle,
	}
	configProgressService := &service.ConfigProgressService{
		Logger:         zapLogger,
		ConfigProgress: configProgress,
		BaseServer:     baseServer,
	}
	configProgressHandle := &handlev1.ConfigProgressHandle{
		BaseHandle:            baseHandle,
		ConfigProgressService: configProgressService,
	}
	configProgressRouter := &v12.ConfigProgressRouter{
		ConfigProgressHandle: configProgressHandle,
	}
	pageDataService := &service.PageDataService{
		BaseServer:         baseServer,
		Logger:             zapLogger,
		Policy:             policy,
		Rule:               rule,
		FilterField:        filterField,
		CheckPoint:         checkPoint,
		EventField:         eventField,
		RuleParameter:      ruleParameter,
		RuleParameterGroup: ruleParameterGroup,
		RuleParameterValue: ruleParameterValue,
		BusinessTypeRepo:   businessType,
	}

	pageDataHandle := &handlev1.PageDataHandle{
		BaseHandle:      baseHandle,
		PolicyService:   policyService,
		PageDataService: pageDataService,
	}
	pageDataRouter := &v12.PageDataRouter{
		PageDataHandle: pageDataHandle,
	}
	rmsCraFramework := &data.RmsCraFramework{
		Base: base,
	}
	rmsCraParameter := &data.RmsCraParameter{
		Base: base,
	}
	rmsCraParameterVal := &data.RmsCraParameterVal{
		Base: base,
	}

	rmsCraScoreReference := &data.RmsCraScoreReference{
		Base: base,
	}
	craFrameWorkService := &service.CraFrameWorkService{
		Logger:       zapLogger,
		CraFramework: rmsCraFramework,
	}

	craParameterService := &service.CraParameterService{
		Logger:             zapLogger,
		ICraFramework:      rmsCraFramework,
		ICraParameter:      rmsCraParameter,
		ICraParameterVal:   rmsCraParameterVal,
		ICraScoreReference: rmsCraScoreReference,
		Conf:               cfg,
	}
	craScoreReferenceService := &service.CraScoreReferenceService{
		Logger:             zapLogger,
		IcraScoreReference: rmsCraScoreReference,
	}

	craParameterValService := &service.CraParameterValService{
		Logger:           zapLogger,
		IcraParameterVal: rmsCraParameterVal,
		ICraParameter:    rmsCraParameter,
	}

	craFrameworkHandle := &handlev1.CraFrameworkHandle{
		BaseHandle:          baseHandle,
		CraFrameworkService: craFrameWorkService,
	}
	craParameterHandle := &handlev1.CraParameterHandle{
		BaseHandle:          baseHandle,
		CraParameterService: craParameterService,
	}
	craParameterValHandle := &handlev1.CraParameterValHandle{
		BaseHandle:             baseHandle,
		CraParameterValService: craParameterValService,
	}
	craScoreReferenceHandle := &handlev1.CraScoreReferenceHandle{
		BaseHandle:               baseHandle,
		CraScoreReferenceService: craScoreReferenceService,
	}
	craFrameworkRouter := &v12.CraFrameworkRouter{
		CraFrameworkHandle: craFrameworkHandle,
	}
	craParameterRouter := &v12.CraParameterRouter{
		CraParameterHandle: craParameterHandle,
	}
	craParameterValRouter := &v12.CraParameterValRouter{
		CraParameterValHandle: craParameterValHandle,
	}
	craScoreReferenceRouter := &v12.CraScoreReferenceRouter{
		CraScoreReferenceHandle: craScoreReferenceHandle,
	}

	IRiskEventRecord := &data.AlertQuery{
		Base: Dbase,
	}

	IFilterExtra := &data.FilterExtra{
		Base: base,
	}

	alertAQueryService := &service.AlertAQueryService{
		Logger:           zapLogger,
		IRiskEventRecord: IRiskEventRecord,
		IRiskFileExtra:   IFilterExtra,
	}
	alertQueryHandle := &handlev1.AlertQueryHandle{
		BaseHandle:         baseHandle,
		AlertAQueryService: alertAQueryService,
	}
	AlertQueryRouter := &v12.AlertQueryRouter{
		AlertQueryHandle: alertQueryHandle,
	}

	IRiskFileExtra := &data.FilterExtra{
		Base: base,
	}
	EventExtartService := &service.EventExtartService{
		Logger:         zapLogger,
		IRiskFileExtra: IRiskFileExtra,
	}
	eventExtraHandle := &handlev1.EventExtraHandle{
		BaseHandle:         baseHandle,
		EventExtartService: EventExtartService,
	}
	fileExtraRouter := &v12.FileExtraRouter{
		EventExtraHandle: eventExtraHandle,
	}
	sysUtilsRedisHandle := &handlev1.SysUtilsRedisHandle{BaseHandle: baseHandle}
	sysUtilsRedisRouter := &v12.SysUtilsRedisRouter{RedisUtilsHandle: sysUtilsRedisHandle}

	// v2版本相关代码
	// V2Service初始化代码
	indicatorServiceV2 := &servicev2.IndicatorService{
		IndicatorRepo:        datav2.NewRmsIndicator(),
		IndicatorVersionRepo: datav2.NewRmsIndicatorVersion(),
		IndicatorRuleRepo:    datav2.NewRmsIndicatorRule(),
		IndicatorMeasureRepo: datav2.NewRmsIndicatorMeasure(),
		IndicatorHistoryRepo: datav2.NewRmsIndicatorVersionHistory(),
		DataSourceTableRepo:  datav2.NewRmsDataSourceTable(),
		DataSourceRepo:       datav2.NewRmsDataSource(),
	}
	blackWhiteListService := &servicev2.BlackWhiteListService{
		BlackWhiteListRepo:        datav2.NewRmsBlackWhiteList(),
		BlackWhiteItemRepo:        datav2.NewRmsBlackWhiteItem(),
		BlackWhiteFieldRepo:       datav2.NewRmsBlackWhiteField(),
		BlackWhiteAuditRepo:       datav2.NewRmsBlackWhiteAudit(),
		BlackWhiteAuditDetailRepo: datav2.NewRmsBlackWhiteAuditDetail(),
		RmsEventFieldRepo:         datav2.NewRmsEventField(),
		RmsEventFieldBindingRepo:  datav2.NewRmsEventFieldBinding(),
	}
	dataSourceServiceV2 := &servicev2.DataSourceService{
		DataSourceRepo:      datav2.NewRmsDataSource(),
		DataSourceTableRepo: datav2.NewRmsDataSourceTable(),
	}
	blackWhiteItemService := &servicev2.BlackWhiteItemService{
		BlackWhiteListRepo:  datav2.NewRmsBlackWhiteList(),
		BlackWhiteItemRepo:  datav2.NewRmsBlackWhiteItem(),
		BlackWhiteFieldRepo: datav2.NewRmsBlackWhiteField(),
		BlackWhiteAuditRepo: datav2.NewRmsBlackWhiteAudit(),
	}
	ruleParameterValueServiceV2 := &servicev2.RuleParameterValueService{
		RuleParameterValueRepo: datav2.NewRmsRuleParameterValue(),
		RuleParameterGroupRepo: datav2.NewRmsRuleParameterGroup(),
		RuleParameterRepo:      datav2.NewRmsRuleParameter(),
	}
	handleLogServiceV2 := &servicev2.HandleLogService{
		HandleLogRepo: datav2.NewWpHandleLog(),
	}
	ruleParameterGroupServiceV2 := &servicev2.RuleParameterGroupService{
		RuleParameterGroupRepo:     datav2.NewRmsRuleParameterGroup(),
		RuleParameterGroupBindRepo: datav2.NewRmsRuleParameterGroupBind(),
	}
	ruleParameterGroupBindServiceV2 := &servicev2.RuleParameterGroupBindService{
		RuleParameterGroupBindRepo: datav2.NewRmsRuleParameterGroupBind(),
		RuleParameterGroup:         datav2.NewRmsRuleParameterGroup(),
	}
	ruleParameterServiceV2 := &servicev2.RuleParameterService{
		RuleParameterRepo: datav2.NewRmsRuleParameter(),
	}
	ruleServiceV2 := &servicev2.RuleService{
		RuleRepo: datav2.NewRmsRule(),
		Conf:     cfg,
	}
	riskLogServiceV2 := &servicev2.RiskLogService{
		RiskLogRepo: datav2.NewRmsRiskLog(),
	}
	policyServiceV2 := &servicev2.PolicyService{
		PolicyRepo: datav2.NewRmsPolicy(),
	}
	operatorLogServiceV2 := &servicev2.OperatorLogService{
		OperatorLogRepo: datav2.NewRmsOperatorLog(),
	}
	filterFieldServiceV2 := &servicev2.FilterFieldService{
		FilterFieldRepo: datav2.NewRmsFilterField(),
	}
	eventServiceV2 := &servicev2.EventService{
		EventRepo: datav2.NewRmsEvent(),
	}
	eventFieldServiceV2 := &servicev2.EventFieldService{
		EventFieldRepo:        datav2.NewRmsEventField(),
		CheckPoint:            datav2.NewRmsCheckPoint(),
		FilterField:           datav2.NewRmsFilterField(),
		PolicyRepo:            datav2.NewRmsPolicy(),
		EventFieldBindingRepo: datav2.NewRmsEventFieldBinding(),
	}
	eventStoreCfgServiceV2 := &servicev2.EventStoreCfgService{
		EventStoreCfgRepo: datav2.NewRmsEventStoreCfg(),
	}
	blackWhiteAuditServiceV2 := &servicev2.BlackWhiteAuditService{
		BlackWhiteListRepo:        datav2.NewRmsBlackWhiteList(),
		BlackWhiteItemRepo:        datav2.NewRmsBlackWhiteItem(),
		BlackWhiteFieldRepo:       datav2.NewRmsBlackWhiteField(),
		BlackWhiteAuditRepo:       datav2.NewRmsBlackWhiteAudit(),
		BlackWhiteAuditDetailRepo: datav2.NewRmsBlackWhiteAuditDetail(),
		BlackWhiteListService:     blackWhiteListService,
	}
	blackWhiteListService.BlackWhiteAuditService = blackWhiteAuditServiceV2
	blackWhiteOperatorLogServiceV2 := &servicev2.BlackWhiteOperatorLogService{
		BlackWhiteOperatorLogRepo: datav2.NewRmsBlackWhiteOperatorLog(),
	}
	blackWhiteFieldServiceV2 := &servicev2.BlackWhiteFieldService{
		BlackWhiteFieldRepo: datav2.NewRmsBlackWhiteField(),
		BlackWhiteListRepo:  datav2.NewRmsBlackWhiteList(),
		BlackWhiteItemRepo:  datav2.NewRmsBlackWhiteItem(),
		BlackWhiteAuditRepo: datav2.NewRmsBlackWhiteAudit(),
		RmsEventFieldRepo:   datav2.NewRmsEventField(),
	}
	// V2Handle初始化代码
	indicatorHandleV2 := &handlev2.IndicatorHandle{IndicatorService: indicatorServiceV2}
	blackWhiteListHandle := &handlev2.BlackWhiteListHandle{
		BlackWhiteListService:  blackWhiteListService,
		BlackWhiteItemService:  blackWhiteItemService,
		BlackWhiteAuditService: blackWhiteAuditServiceV2,
	}
	dataSourceHandleV2 := &handlev2.DataSourceHandle{
		DataSourceService: dataSourceServiceV2,
	}
	blackWhiteItemHandleV2 := &handlev2.BlackWhiteItemHandle{
		BlackWhiteItemService: blackWhiteItemService,
	}
	ruleParameterValueHandleV2 := &handlev2.RuleParameterValueHandle{RuleParameterValueService: ruleParameterValueServiceV2}
	ruleParameterGroupHandleV2 := &handlev2.RuleParameterGroupHandle{RuleParameterGroupService: ruleParameterGroupServiceV2}
	ruleParameterHandleV2 := &handlev2.RuleParameterHandle{RuleParameterService: ruleParameterServiceV2}
	ruleParameterGroupBindHandleV2 := &handlev2.RuleParameterGroupBindHandle{RuleParameterGroupBindService: ruleParameterGroupBindServiceV2}
	ruleHandleV2 := &handlev2.RuleHandle{RuleService: ruleServiceV2}
	policyHandleV2 := &handlev2.PolicyHandle{PolicyService: policyServiceV2}
	operatorLogHandleV2 := &handlev2.OperatorLogHandle{OperatorLogService: operatorLogServiceV2}
	filterFieldHandleV2 := &handlev2.FilterFieldHandle{FilterFieldService: filterFieldServiceV2}
	eventFieldHandleV2 := &handlev2.EventFieldHandle{EventFieldService: eventFieldServiceV2}
	eventHandleV2 := &handlev2.EventHandle{EventService: eventServiceV2}
	handleLogHandleV2 := &handlev2.HandleLogHandle{
		HandleLogService: handleLogServiceV2,
	}
	riskLogHandleV2 := &handlev2.RiskLogHandle{RiskLogService: riskLogServiceV2}
	eventStoreCfgHandleV2 := &handlev2.EventStoreCfgHandle{EventStoreCfgService: eventStoreCfgServiceV2}
	blackWhiteAuditHandleV2 := &handlev2.BlackWhiteAuditHandle{BlackWhiteAuditService: blackWhiteAuditServiceV2}
	blackWhiteOperatorLogHandleV2 := &handlev2.BlackWhiteOperatorLogHandle{BlackWhiteOperatorLogService: blackWhiteOperatorLogServiceV2}
	blackWhiteFieldHandleV2 := &handlev2.BlackWhiteFieldHandle{BlackWhiteFieldService: blackWhiteFieldServiceV2}
	// V2Router初始化代码
	indicatorRouterV2 := &v2.IndicatorRouter{
		IndicatorHandle: indicatorHandleV2,
	}

	blackWhiteListRouter := &v2.BlackWhiteListRouter{
		BlackWhiteListHandle:        blackWhiteListHandle,
		BlackWhiteItemHandle:        blackWhiteItemHandleV2,
		BlackWhiteAuditHandle:       blackWhiteAuditHandleV2,
		BlackWhiteFieldHandle:       blackWhiteFieldHandleV2,
		BlackWhiteOperatorLogHandle: blackWhiteOperatorLogHandleV2,
	}
	dataSourceRouterV2 := &v2.DataSourceRouter{
		DataSourceHandle: dataSourceHandleV2,
	}
	ruleParameterValueRouterV2 := &v2.RuleParameterValueRouter{RuleParameterValueHandle: ruleParameterValueHandleV2}
	ruleParameterGroupBindRouterV2 := &v2.RuleParameterGroupBindRouter{RuleParameterGroupBindHandle: ruleParameterGroupBindHandleV2}
	ruleParameterGroupRouterV2 := &v2.RuleParameterGroupRouter{RuleParameterGroupHandle: ruleParameterGroupHandleV2}
	ruleParameterRouterV2 := &v2.RuleParameterRouter{RuleParameterHandle: ruleParameterHandleV2}
	ruleRouterV2 := &v2.RuleRouter{RuleHandle: ruleHandleV2}
	riskLogRouterV2 := &v2.RiskLogRouter{RiskLogHandle: riskLogHandleV2}
	policyRouterV2 := &v2.PolicyRouter{PolicyHandle: policyHandleV2}
	operatorLogRouterV2 := &v2.OperatorLogRouter{OperatorLogHandle: operatorLogHandleV2}
	filterFieldRouterV2 := &v2.FilterFieldRouter{FilterFieldHandle: filterFieldHandleV2}
	eventFieldRouterV2 := &v2.EventFieldRouter{EventFieldHandle: eventFieldHandleV2}
	eventRouterV2 := &v2.EventRouter{EventHandle: eventHandleV2}
	eventStoreCfgRouterV2 := &v2.EventStoreCfgRouter{EventStoreCfgHandle: eventStoreCfgHandleV2}
	handleLogRouterV2 := &v2.HandleLogRouter{
		HandleLogHandle: handleLogHandleV2,
	}

	dataSourceTableServiceV2 := &servicev2.DataSourceTableService{
		DataSourceTableRepo: datav2.NewRmsDataSourceTable(),
		DataSourceRepo:      datav2.NewRmsDataSource(),
		IndicatorRepo:       datav2.NewRmsIndicator(),
	}
	dataSourceTableHandleV2 := &handlev2.DataSourceTableHandle{
		DataSourceTableService: dataSourceTableServiceV2,
	}
	dataSourceTableRouterV2 := &v2.DataSourceTableRouter{
		DataSourceTableHandle: dataSourceTableHandleV2,
	}

	routersV1 := engine.NewRouters().
		SetV1Routers(alarmContactRouter, alarmTimeConfRouter, blacklistRouter, businessTypeRouter, checkPointRouter, eventRouter, eventFieldRouter, eventStoreCfgRouter, filterFieldRouter, operatorLogRouter,
			policyRouter, riskLogRouter, ruleRouter, ruleParameterRouter, ruleParameterGroupRouter, ruleParameterGroupBindRouter, ruleParameterValueRouter, handleLogRouter, configProgressRouter, pageDataRouter,
			craFrameworkRouter, craParameterRouter, craParameterValRouter, craScoreReferenceRouter, sysUtilsRedisRouter, AlertQueryRouter, fileExtraRouter).
		SetV2Routers(blackWhiteListRouter, ruleParameterValueRouterV2, handleLogRouterV2, ruleParameterGroupBindRouterV2, ruleParameterGroupRouterV2, ruleParameterRouterV2, ruleRouterV2,
			riskLogRouterV2, policyRouterV2, operatorLogRouterV2, filterFieldRouterV2, eventRouterV2, eventFieldRouterV2, eventStoreCfgRouterV2, dataSourceRouterV2, indicatorRouterV2, dataSourceTableRouterV2)

	jwtJWT := jwt.NewJWT(cfg)
	engineBase := engine.NewEngineBase(cfg, routersV1, zapLogger, &jwtJWT)
	enginePro := &engine.EnginePro{
		EngineBase: engineBase,
		JWT:        &jwtJWT,
	}
	cron.Start()
	v, err := newApp(enginePro)
	if err != nil {
		return nil, nil, err
	}
	return v, func() {
	}, nil
}

// wire.go:

func newApp(e *engine.EnginePro) (func() error, error) {
	return e.Run, nil
}

func Run() error {
	run, clean, err := wireApp()
	if err != nil {
		return err
	}
	defer clean()
	return run()
}
