package handlev2

import (
	"errors"
	jsoniter "github.com/json-iterator/go"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

// IndicatorHandle 指标配置处理器
type IndicatorHandle struct {
	BaseHandle
	IndicatorService *servicev2.IndicatorService
}

// Create 创建指标配置
// @Summary Create Indicator 创建指标配置
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CreateIndicator true "Request body"
// @Success 200 {object} response_parameters.IndicatorCreateResponse "指标配置创建成功"
// @Router /api/v2/indicator [post]
func (h *IndicatorHandle) Create(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.CreateIndicator](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	err = req.Validator()
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	// 创建指标配置
	id, versionID, err := h.IndicatorService.Create(c, &req)
	if response.HeadErr(c, err) {
		return
	}
	err = h.IndicatorService.GenerateVersionLog(c, h.GetUsername(c), id)
	if response.HeadErr(c, err) {
		return
	}
	// 返回创建结果
	response.ReturnSuccess(c, response_parameters.IndicatorCreateResponse{
		IndicatorId: id,
		VersionID:   versionID,
	})
}

// Update 更新指标配置
// @Summary Update Indicator 更新指标配置
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.UpdateIndicator true "Request body"
// @Success 200 {object} nil "指标配置更新成功"
// @Router /api/v2/indicator [put]
func (h *IndicatorHandle) Update(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.UpdateIndicator](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	// 更新指标配置
	err = h.IndicatorService.Update(c, &req)
	if response.HeadErr(c, err) {
		return
	}
	err = h.IndicatorService.GenerateVersionLog(c, h.GetUsername(c), req.ID)
	if response.HeadErr(c, err) {
		return
	}
	// 返回更新结果
	response.ReturnSuccess(c, nil)
}

// List 获取指标配置列表
// @Summary Get Indicator List 获取指标配置列表
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.GetIndicatorList true "Request body"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.IndicatorList]
// @Router /api/v2/indicator/list [post]
func (h *IndicatorHandle) List(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.GetIndicatorList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 获取指标配置列表
	total, items, err := h.IndicatorService.List(c, &req)
	if response.HeadErr(c, err) {
		return
	}

	// 返回列表结果
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.IndicatorList]{
		TotalNum:  total,
		Items:     items,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// Delete 删除指标配置
// @Summary Delete Indicator 删除指标配置
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "指标ID"
// @Success 200 {object} nil "指标配置删除成功"
// @Router /api/v2/indicator/:id [delete]
func (h *IndicatorHandle) Delete(c *gin.Context) {
	// 解析请求参数
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 删除指标配置
	err = h.IndicatorService.Delete(c, id)
	if response.HeadErr(c, err) {
		return
	}

	// 返回删除结果
	response.ReturnSuccess(c, nil)
}

// Retrieve 获取指标配置详情
// @Summary Get Indicator Detail 获取指标配置详情
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id query int true "指标ID"
// @Param version_id query int true "版本短ID"
// @Success 200 {object} response_parameters.IndicatorDetail
// @Router /api/v2/indicator [get]
func (h *IndicatorHandle) Retrieve(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestQueryBind[request_parameters.GetIndicatorDetail](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 获取指标配置详情
	detail, err := h.IndicatorService.Retrieve(c, req.ID, req.VersionID)
	if response.HeadErr(c, err) {
		return
	}

	// 返回详情结果
	response.ReturnSuccess(c, detail)
}

// ListVersions 获取指标配置版本列表
// @Summary Get Indicator Version List 获取指标配置版本列表
// @Tags risk-portal-api/Indicator 指标/Version 版本管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.GetIndicatorVersion true "Request body"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.IndicatorVersionList]
// @Router /api/v2/indicator/versions [post]
func (h *IndicatorHandle) ListVersions(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.GetIndicatorVersion](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 获取指标配置版本列表
	total, items, err := h.IndicatorService.ListVersions(c, &req)
	if response.HeadErr(c, err) {
		return
	}

	// 返回列表结果
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.IndicatorVersionList]{
		TotalNum:  total,
		Items:     items,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// Release 发布指标版本
// @Summary Release Indicator Version 发布指标版本
// @Tags risk-portal-api/Indicator 指标/Version 版本管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.ReleaseIndicator true "Request body"
// @Success 200 {object} nil "发布成功"
// @Router /api/v2/indicator/version/release [post]
func (h *IndicatorHandle) Release(c *gin.Context) {
	// 1. 获取请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.ReleaseIndicator](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 2. 调用服务发布指标版本
	err = h.IndicatorService.ReleaseVersion(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	// 3. 刷新指标缓存
	err = h.IndicatorService.FlushIndicatorCache(c, req.ID)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	// 4. 返回成功响应
	response.ReturnSuccess(c, nil)
}

// UpdateStatus 更新指标状态
// @Summary Update Indicator Status 更新指标状态
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.UpdateIndicatorStatus true "Request body"
// @Success 200 {object} nil "指标状态更新成功"
// @Router /api/v2/indicator/status [post]
func (h *IndicatorHandle) UpdateStatus(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.UpdateIndicatorStatus](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 更新指标状态
	err = h.IndicatorService.UpdateStatus(c, &req)
	if response.HeadErr(c, err) {
		return
	}
	if req.Disable {
		//禁用，删除缓存
		// 刷新指标缓存
		err = h.IndicatorService.DeleteIndicatorCache(c, req.ID)
		if err != nil {
			response.ReturnFailure(c, err)
			return
		}
	} else {
		// 刷新指标缓存
		err = h.IndicatorService.FlushIndicatorCache(c, req.ID)
		if err != nil {
			response.ReturnFailure(c, err)
			return
		}
	}
	// 返回更新结果
	response.ReturnSuccess(c, nil)
}

// TestIndicatorSql 测试指标SQL
// @Summary Test Indicator SQL 测试指标SQL
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.TestIndicatorSql true "Request body"
// @Success 200 {object} []map[string]interface{} "指标SQL测试成功"
// @Router /api/v2/indicator/testing [post]
func (h *IndicatorHandle) TestIndicatorSql(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.TestIndicatorSql](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var testData []map[string]interface{}
	for _, datum := range req.TestData {
		tmpData := make(map[string]interface{})
		err = jsoniter.UnmarshalFromString(datum, &tmpData)
		if err != nil {
			response.ReturnFailure(c, errors.New("Test data format error"))
			return
		}
		testData = append(testData, tmpData)
	}
	var res []map[string]interface{}
	// 生成指标SQL
	res, err = h.IndicatorService.TestIndicatorSql(c, testData, req.IndicatorID, req.VersionID)
	if response.HeadErr(c, err) {
		return
	}
	response.ReturnSuccess(c, res)

}

// GenerateIndicatorSql 生成指标SQL
// @Summary Generate Indicator SQL 生成指标SQL
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.GenerateIndicatorSql true "Request body"
// @Success 200 {object} response_parameters.GenerateIndicatorSqlResponse "指标SQL生成成功"
// @Router /api/v2/indicator/generate_sql [post]
func (h *IndicatorHandle) GenerateIndicatorSql(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.GenerateIndicatorSql](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var preData *entity.Indicator
	// 生成指标SQL
	preData, err = h.IndicatorService.GetIndicatorSqlData(c, req.IndicatorID, req.VersionID)
	if response.HeadErr(c, err) {
		return
	}
	err = preData.Validator()
	if err != nil {
		response.ReturnFailure(c, er.Internal.WithMsg(err.Error()))
		return
	}
	var sql string
	var placeholders []string
	switch preData.DataSourceType {
	case "mysql":
		sql, placeholders, err = h.IndicatorService.Indicator2MysqlSql(preData)
		if response.HeadErr(c, err) {
			return
		}
	case "doris":
		sql, placeholders, err = h.IndicatorService.Indicator2DorisSql(preData)
		if response.HeadErr(c, err) {
			return
		}
	}

	response.ReturnSuccess(c, response_parameters.GenerateIndicatorSqlResponse{
		Sql:          sql,
		Placeholders: placeholders,
	})
}
