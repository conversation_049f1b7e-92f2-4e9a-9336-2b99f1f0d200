package handlev2

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
)

// DataSourceHandle 数据源处理器
type DataSourceHandle struct {
	BaseHandle
	DataSourceService *servicev2.DataSourceService
}

// Create 创建数据源
// @Summary Create DataSource 创建数据源
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.DataSourceCreate true "数据源信息"
// @Success 200 {object} domain.Base{data=response_parameters.DataSourceDetail} "数据源创建成功"
// @Router /api/v2/data_source [post]
func (h *DataSourceHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.DataSourceCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}

	// 调用服务层创建数据源
	resp, err := h.DataSourceService.Create(c.Request.Context(), &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, resp)
}

// Update 更新数据源
// @Summary Update DataSource 更新数据源
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.DataSourceUpdate true "数据源更新信息"
// @Success 200 {object} domain.Base{data=response_parameters.DataSourceDetail} "数据源更新成功"
// @Router /api/v2/data_source [put]
func (h *DataSourceHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.DataSourceUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}

	// 调用服务层更新数据源
	resp, err := h.DataSourceService.Update(c.Request.Context(), &req)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, resp)
}

// Delete 删除数据源
// @Summary Delete DataSource 删除数据源
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "数据源ID"
// @Success 200 {object} domain.Base "数据源删除成功"
// @Router /api/v2/data_source/:id [delete]
func (h *DataSourceHandle) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}

	// 调用服务层删除数据源
	err = h.DataSourceService.Delete(c.Request.Context(), id)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, nil)
}

// List 获取数据源列表
// @Summary Get DataSource List 获取数据源列表
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.DataSourceList true "查询条件"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.DataSourceDetail]
// @Router /api/v2/data_source/list [post]
func (h *DataSourceHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.DataSourceList](c)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}

	// 调用服务层获取数据源列表
	total, res, err := h.DataSourceService.List(c.Request.Context(), &req)
	if response.HeadErr(c, err) {
		return
	}
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.DataSourceList]{
		TotalNum:  total,
		Items:     res,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// Retrieve 获取数据源详情
// @Summary Get DataSource Detail 获取数据源详情
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "数据源ID"
// @Success 200 {object} domain.Base{data=response_parameters.DataSourceDetail} "数据源详情"
// @Router /api/v2/data_source/:id [get]
func (h *DataSourceHandle) Retrieve(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}

	// 调用服务层获取数据源详情
	resp, err := h.DataSourceService.Retrieve(c.Request.Context(), id)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, resp)
}

// TestConnection 测试数据源连接
// @Summary Test DataSource Connection 测试数据源连接
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.DataSourceTest true "数据源连接信息"
// @Success 200 {object} domain.Base{data=response_parameters.DataSourceTestResp} "连接测试结果"
// @Router /api/v2/data_source/test_connection [post]
func (h *DataSourceHandle) TestConnection(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.DataSourceTest](c)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}

	// 调用服务层测试数据源连接
	resp, err := h.DataSourceService.TestConnection(c.Request.Context(), &req)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, resp)
}

// Tables 获取数据源表列表
// @Summary Get DataSource Tables 获取数据源表列表
// @Tags risk-portal-api/DataSource 数据源管理/Config 数据源配置
// @Security x-auth-token
// @Param data_source_id path int true "数据源ID"
// @Success 200 {array} string  "表列表"
// @Router /api/v2/data_source/tables/:data_source_id [get]
func (h *DataSourceHandle) Tables(c *gin.Context) {
	idStr := c.Param("data_source_id")
	dataSourceID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)

		return
	}
	var res []*entity.DataSourceTable
	res, err = h.DataSourceService.Tables(c.Request.Context(), dataSourceID)
	if response.HeadErr(c, err) {
		return
	}
	var tables []string
	for _, re := range res {
		tables = append(tables, re.TableName)
	}
	response.ReturnSuccess(c, tables)
}
