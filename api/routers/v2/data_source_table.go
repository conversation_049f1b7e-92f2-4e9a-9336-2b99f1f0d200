package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
)

// DataSourceTableRouter 数据源表信息路由
type DataSourceTableRouter struct {
	DataSourceTableHandle *handlev2.DataSourceTableHandle
}

// Register 注册路由
func (r *DataSourceTableRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/data_source_table")
	g.POST("", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create data source table"), r.DataSourceTableHandle.Create)
	g.DELETE("/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "Delete data source table"), r.DataSourceTableHandle.Delete)
	g.POST("/list", r.DataSourceTableHandle.List)
	g.GET("/:id", r.DataSourceTableHandle.Retrieve)
	g.GET("columns", r.DataSourceTableHandle.Columns) // 获取数据源表字段列表

}
