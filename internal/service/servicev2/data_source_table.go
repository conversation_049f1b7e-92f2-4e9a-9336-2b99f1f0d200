package servicev2

import (
	"context"
	"errors"
	"fmt"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/cache"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// DataSourceTableService 数据源表信息服务
type DataSourceTableService struct {
	DataSourceTableRepo repo.DataSourceTableRepo
	DataSourceRepo      repo.DataSourceRepo
	IndicatorRepo       repo.IndicatorRepo
}

var dataSourceColumnCache cache.ICache[[]*entity.DataSourceColumn] = cache.NewLocalFreeCache[[]*entity.DataSourceColumn](1024 * 1024)

func (s *DataSourceTableService) Columns(ctx context.Context, tableID int64) (res []*entity.DataSourceColumn, err error) {
	var dataSourceTable *modelv2.RmsDataSourceTable
	dataSourceTable, err = s.DataSourceTableRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", tableID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = er.NotFound.WithMsg("Data source table not found")
		return
	}
	if err != nil {
		err = er.Internal.WithErr(err)
		return
	}
	var ok bool
	if ok, err = dataSourceColumnCache.Has(fmt.Sprintf("%d-columns", tableID)); err != nil {
		return nil, er.Internal.WithErr(err)
	}
	if ok {
		res, err = dataSourceColumnCache.Get(fmt.Sprintf("%d-columns", tableID))
		if err != nil {
			return nil, er.Internal.WithErr(err)
		}
		return res, nil
	}
	var dataSource *modelv2.RmsDataSource
	dataSource, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", dataSourceTable.DataSourceID)
	})
	if err != nil {
		err = er.NotFound.WithMsg("Data source not found")
		return
	}
	var tmpColumns []*entity.Column
	tmpColumns, err = s.DataSourceTableRepo.Columns(ctx, dataSource, dataSourceTable.Name)
	if err != nil {
		return
	}
	for _, column := range tmpColumns {
		res = append(res, &entity.DataSourceColumn{
			TableID:    tableID,
			ColumnName: column.Name,
			ColumnID:   entity.NewDataSourceColumnID(tableID, column.Name),
			FieldType:  column.TransType(),
			DataType:   column.DataType,
			ColumnType: column.ColumnType,
		})
	}

	dataSourceColumnCache.Set(fmt.Sprintf("%d-columns", tableID), res, 10)
	return res, nil
}

// Create 创建数据源表信息
func (s *DataSourceTableService) Create(ctx context.Context, userName string, req request_parameters.CreateDataSourceTable) (int64, error) {
	// 验证业务类型
	if !req.ValidateBusinessType() {
		return 0, er.Internal.WithMsg("Invalid business type")
	}

	// 检查数据源是否存在
	_, err := s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.DataSourceID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, er.NotFound.WithMsg("Data source does not exist")
		}
		return 0, er.Internal.WithErr(err)
	}

	// 检查表名是否已存在
	count, err := s.DataSourceTableRepo.Count(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("data_source_id = ?", req.DataSourceID).Where("name = ?", req.Name)
	})
	if err != nil {
		return 0, er.Internal.WithErr(err)
	}
	if count > 0 {
		return 0, er.AlreadyExists.WithMsg("Table name already exists in this data source")
	}

	// 创建数据源表信息
	dataSourceTable := &modelv2.RmsDataSourceTable{
		DataSourceID:   req.DataSourceID,
		Name:           req.Name,
		Alias:          req.Alias,
		Desc:           req.Desc,
		BusinessType:   req.BusinessType,
		LastModifiedBy: userName,
	}

	err = s.DataSourceTableRepo.Create(ctx, nil, dataSourceTable)
	if err != nil {
		return 0, err
	}

	return dataSourceTable.ID, nil
}

// Update 更新数据源表信息
//func (s *DataSourceTableService) Update(ctx context.Context, userName string, req request_parameters.UpdateDataSourceTable) (int64, error) {
//	// 验证业务类型
//
//	// 检查数据源表信息是否存在
//	dataSourceTable, err := s.DataSourceTableRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
//		return db.Where("id = ?", req.ID)
//	})
//	if err != nil {
//		if errors.Is(err, gorm.ErrRecordNotFound) {
//			return 0, er.NotFound.WithMsg("Data does not exist")
//		}
//		return 0, er.Internal.WithErr(err)
//	}
//
//	// 检查数据源是否存在
//	_, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
//		return db.Where("id = ?", req.DataSourceID)
//	})
//	if err != nil {
//		if errors.Is(err, gorm.ErrRecordNotFound) {
//			return 0, er.NotFound.WithMsg("Data source does not exist")
//		}
//		return 0, er.Internal.WithErr(err)
//	}
//
//	// 检查表名是否已存在（排除自身）
//	count, err := s.DataSourceTableRepo.Count(ctx, func(db *gorm.DB) *gorm.DB {
//		return db.Where("data_source_id = ?", req.DataSourceID).Where("name = ?", req.Name).Where("id != ?", req.ID)
//	})
//	if err != nil {
//		return 0, er.Internal.WithErr(err)
//	}
//	if count > 0 {
//		return 0, er.AlreadyExists.WithMsg("Table name already exists in this data source")
//	}
//
//	// 更新数据源表信息
//	dataSourceTable.DataSourceID = req.DataSourceID
//	dataSourceTable.Name = req.Name
//	dataSourceTable.Alias = req.Alias
//	dataSourceTable.Desc = req.Desc
//	dataSourceTable.BusinessType = req.BusinessType
//	dataSourceTable.LastModifiedBy = userName
//
//	err = s.DataSourceTableRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
//		return db.Where("id = ?", req.ID)
//	}, dataSourceTable)
//	if err != nil {
//		return 0, err
//	}
//
//	return dataSourceTable.ID, nil
//}

// Delete 删除数据源表信息
func (s *DataSourceTableService) Delete(ctx context.Context, id int64) error {
	// 检查数据源表信息是否存在
	_, err := s.DataSourceTableRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return er.NotFound.WithMsg("Data does not exist")
		}
		return er.Internal.WithErr(err)
	}

	// 删除数据源表信息
	err = s.DataSourceTableRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return er.Internal.WithErr(err)
	}

	return nil
}

// List 获取数据源表信息列表
func (s *DataSourceTableService) List(ctx context.Context, req request_parameters.GetDataSourceTableList) (total int64, res []*response_parameters.DataSourceTableList, err error) {
	// 构建查询条件
	total, items, err := s.DataSourceTableRepo.List(ctx, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.DataSourceID > 0 {
			db = db.Where("data_source_id = ?", req.DataSourceID)
		}
		if req.Name != "" {
			db = db.Where("name LIKE ?", "%"+req.Name+"%")
		}
		if req.BusinessType > 0 {
			db = db.Where("business_type = ?", req.BusinessType)
		}
		return db.Order("id desc")
	}, true)
	if err != nil {
		return
	}
	var tableIDs []int64
	var tableIDToRes = make(map[int64]*response_parameters.DataSourceTableList)
	// 转换为响应格式
	for _, item := range items {
		tmpRes := &response_parameters.DataSourceTableList{
			ID:               item.ID,
			DataSourceID:     item.DataSourceID,
			Name:             item.Name,
			Alias:            item.Alias,
			BusinessType:     item.BusinessType,
			BusinessTypeName: item.GetBusinessTypeName(),
		}
		tableIDs = append(tableIDs, item.ID)
		res = append(res, tmpRes)
		tableIDToRes[tmpRes.ID] = tmpRes
	}
	if len(tableIDs) > 0 {
		var indicators []*modelv2.RmsIndicator
		_, indicators, err = s.IndicatorRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("table_id in ?", tableIDs).Order("id desc")
		})
		for _, indicator := range indicators {
			tableIDToRes[indicator.TableID].AssociatedIndicator = append(tableIDToRes[indicator.TableID].AssociatedIndicator, indicator.IndicatorName)
		}
	}

	return
}

// Retrieve 获取数据源表信息详情
func (s *DataSourceTableService) Retrieve(ctx context.Context, id int64) (*response_parameters.DataSourceTableDetail, error) {
	// 获取数据源表信息详情
	dataSourceTable, err := s.DataSourceTableRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist")
		}
		return nil, er.Internal.WithErr(err)
	}
	var dataSource *modelv2.RmsDataSource
	dataSource, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", dataSourceTable.DataSourceID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist")
		}
		return nil, er.Internal.WithErr(err)
	}
	var columns []*entity.Column
	columns, err = s.DataSourceTableRepo.Columns(ctx, dataSource, dataSourceTable.Name)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist")
		}
		return nil, er.Internal.WithErr(err)
	}
	var fields []response_parameters.DataSourceTableField
	for _, column := range columns {
		fields = append(fields, response_parameters.DataSourceTableField{
			Name:       column.Name,
			ColumnType: column.ColumnType,
			Desc:       column.Comment,
		})
	}
	// 转换为响应格式
	detail := &response_parameters.DataSourceTableDetail{
		ID:               dataSourceTable.ID,
		DataSourceName:   dataSource.SourceName,
		Alias:            dataSourceTable.Alias,
		Name:             dataSourceTable.Name,
		Desc:             dataSourceTable.Desc,
		BusinessType:     dataSourceTable.BusinessType,
		BusinessTypeName: dataSourceTable.GetBusinessTypeName(),
		Fields:           fields,
		LastModifiedBy:   dataSourceTable.LastModifiedBy,
		UpdatedAt:        dataSourceTable.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return detail, nil
}

// GetTableToSourceType 获取当前数据库中所有表对应的数据库类型
func (c *DataSourceTableService) GetTableToSourceType(ctx context.Context) (tableIDToType map[int64]string, err error) {
	var dataSources []*modelv2.RmsDataSource
	var dataSourceTables []*modelv2.RmsDataSourceTable
	var sourceIDToType = make(map[int64]string)
	tableIDToType = make(map[int64]string)
	_, dataSources, err = c.DataSourceRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("conn_status = ?", modelv2.ConnStatusConnected)
	})
	if err != nil {
		return
	}
	for _, source := range dataSources {
		sourceIDToType[source.ID] = source.SourceType
	}
	_, dataSourceTables, err = c.DataSourceTableRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("conn_status = ?", modelv2.ConnStatusConnected)
	})
	if err != nil {
		return
	}
	for _, table := range dataSourceTables {
		if sourceType, ok := sourceIDToType[table.DataSourceID]; ok {
			tableIDToType[table.ID] = sourceType
		}
	}
	return
}
