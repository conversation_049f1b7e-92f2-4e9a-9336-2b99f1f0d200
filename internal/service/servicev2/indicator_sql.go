package servicev2

import (
	"context"
	"fmt"
	"strings"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gorm.io/gorm"
)

func (s *IndicatorService) genIndicatorTimeWindowSql(indicator *entity.Indicator) (whereParts []string) {
	whereParts = make([]string, 0)
	// 时间窗口
	tw := indicator.TimeWindow
	timeColObj := entity.NewDataSourceColumnByColumnID(tw.TimeWindowColumnID)
	timeCol := string(tw.TimeWindowColumnID)
	if timeColObj != nil {
		timeCol = timeColObj.ColumnName
	}

	// 使用MySQL的NOW()函数替代本地时间
	if tw.TimeWindowType == consts.IndicatorMeasureWindowTypeSliding {
		switch tw.TimeWindowUnit {
		case consts.IndicatorTimeWindowUnitMinutes:
			// 使用MySQL的DATE_SUB函数
			where := fmt.Sprintf(`%s >= DATE_SUB(NOW(), INTERVAL %d MINUTE)`, timeCol, tw.TimeWindowValue)
			whereParts = append(whereParts, where)
		case consts.IndicatorTimeWindowUnitHours:
			where := fmt.Sprintf(`%s >= DATE_SUB(NOW(), INTERVAL %d HOUR)`, timeCol, tw.TimeWindowValue)
			whereParts = append(whereParts, where)
		case consts.IndicatorTimeWindowUnitDays:
			if tw.TimeWindowExcluding {
				whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL %d DAY), '%%Y-%%m-%%d 00:00:00') AND %s < DATE_FORMAT(NOW(), '%%Y-%%m-%%d 00:00:00')`,
					timeCol, tw.TimeWindowValue, timeCol))
			} else {
				// 使用MySQL的DATE_SUB和DATE_FORMAT函数
				whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL %d DAY), '%%Y-%%m-%%d 00:00:00')`,
					timeCol, tw.TimeWindowValue-1))
			}
		case consts.IndicatorTimeWindowUnitWeeks:
			if tw.TimeWindowExcluding {
				whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(
					DATE_SUB(
						DATE_SUB(NOW(), INTERVAL WEEKDAY(NOW()) DAY),
						INTERVAL %d WEEK
					),
					'%%Y-%%m-%%d 00:00:00'
				) AND %s < DATE_FORMAT(
					DATE_SUB(NOW(), INTERVAL WEEKDAY(NOW()) DAY),
					'%%Y-%%m-%%d 00:00:00'
				)`, timeCol, tw.TimeWindowValue, timeCol))
			} else {
				// 使用MySQL的DATE_SUB和WEEKDAY函数处理周
				// WEEKDAY返回0-6，0表示周一，6表示周日
				// 计算到本周一的偏移天数，然后减去周数
				whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(
					DATE_SUB(
						DATE_SUB(NOW(), INTERVAL WEEKDAY(NOW()) DAY),
						INTERVAL %d WEEK
					),
					'%%Y-%%m-%%d 00:00:00'
				)`, timeCol, tw.TimeWindowValue-1))
			}
		case consts.IndicatorTimeWindowUnitMonths:
			if tw.TimeWindowExcluding {
				whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL %d MONTH), '%%Y-%%m-01 00:00:00') AND %s < DATE_FORMAT(NOW(), '%%Y-%%m-01 00:00:00')`,
					timeCol, tw.TimeWindowValue, timeCol))
			} else {
				// 使用MySQL的DATE_SUB和DATE_FORMAT函数处理月
				whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL %d MONTH), '%%Y-%%m-01 00:00:00')`,
					timeCol, tw.TimeWindowValue-1))
			}
		}
	} else if tw.TimeWindowType == consts.IndicatorMeasureWindowTypeFixed {
		switch tw.TimeWindowUnit {
		case consts.IndicatorTimeWindowUnitDaily:
			// 使用MySQL的DATE_FORMAT函数
			whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(NOW(), '%%Y-%%m-%%d 00:00:00')`, timeCol))
		case consts.IndicatorTimeWindowUnitMonthly:
			whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(NOW(), '%%Y-%%m-01 00:00:00')`, timeCol))
		case consts.IndicatorTimeWindowUnitAnnual:
			whereParts = append(whereParts, fmt.Sprintf(`%s >= DATE_FORMAT(NOW(), '%%Y-01-01 00:00:00')`, timeCol))
		}
	} else if tw.TimeWindowType == consts.IndicatorMeasureWindowTypeBefore {
		whereParts = append(whereParts, fmt.Sprintf(`%s < '%s'`, timeCol, tw.TimeWindowStartTime))
	} else if tw.TimeWindowType == consts.IndicatorMeasureWindowTypeAfter {
		whereParts = append(whereParts, fmt.Sprintf(`%s > %s`, timeCol, tw.TimeWindowEndTime))
	} else if tw.TimeWindowType == consts.IndicatorMeasureWindowTypeBetween {
		whereParts = append(whereParts, fmt.Sprintf(`%s BETWEEN %s AND %s`, timeCol, tw.TimeWindowStartTime, tw.TimeWindowEndTime))
	}
	return whereParts
}
func (s *IndicatorService) genIndicatorRuleSql(columnMap map[string]string, indicator *entity.Indicator) (whereParts, placeholders []string) {
	if len(indicator.Rules) > 0 {
		ruleStr := ""
		for i, rule := range indicator.Rules {
			colObj := entity.NewDataSourceColumnByColumnID(rule.ColumnID)
			colName := string(rule.ColumnID)
			if colObj != nil {
				colName = colObj.ColumnName
			}
			if i > 0 && rule.Connector != "" {
				ruleStr += " " + strings.ToUpper(rule.Connector) + " "
			}
			if rule.HasLeftBrackets {
				ruleStr += "("
			}
			switch rule.Operator {
			case "is_true":
				ruleStr += fmt.Sprintf("%s %s %v", colName, "=", true)
			case "is_false":
				ruleStr += fmt.Sprintf("%s %s %v", colName, "=", false)
			default:
				// 操作符转换
				sqlOp, ok := modelv2.IndicatorSQLOperatorMap[rule.Operator]
				if !ok {
					sqlOp = rule.Operator // fallback
				}

				if rule.ValueType == "field" {
					// 字段比较
					placeholders = append(placeholders, rule.Value)
					right := "?"
					ruleStr += fmt.Sprintf("%s %s %s", colName, sqlOp, right)
				} else {
					switch sqlOp {
					case "IN", "NOT IN":
						if columnMap[colName] == "String" {
							var tmpValues []string
							for _, v := range strings.Split(rule.Value, ",") {
								tmpValues = append(tmpValues, fmt.Sprintf("'%s'", v))
							}
							rule.Value = strings.Join(tmpValues, ",")
						}
						// 常量
						ruleStr += fmt.Sprintf(`%s %s (%s)`, colName, sqlOp, rule.Value)
					default:
						// 常量
						right := fmt.Sprintf("'%s'", rule.Value)
						ruleStr += fmt.Sprintf("%s %s %s", colName, sqlOp, right)
					}

				}
			}

			if rule.HasRightBrackets {
				ruleStr += ")"
			}
		}
		if ruleStr != "" {
			whereParts = append(whereParts, ruleStr)
		}
	}
	return
}

// Indicator2MysqlSql 将指标配置转换为SQL语句
func (s *IndicatorService) Indicator2MysqlSql(indicator *entity.Indicator) (string, []string, error) {
	// 将指标配置转换为SQL语句
	// 1. 获取表名
	var placeholders []string
	var err error
	var table *modelv2.RmsDataSourceTable
	table, err = s.DataSourceTableRepo.First(context.Background(), func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", indicator.TableID)
	})
	if err != nil {
		return "", placeholders, er.Internal.WithMsg("Table ID is invalid")
	}
	var columnMap = make(map[string]string)
	columnMap, err = s.getColumnMapToType(table)
	if err != nil {
		return "", placeholders, er.Internal.WithMsg("column is invalid")
	}
	// 2. 生成SELECT
	selects := make([]string, 0, len(indicator.Measures))
	for _, m := range indicator.Measures {
		colObj := entity.NewDataSourceColumnByColumnID(m.MeasureField)
		colName := string(m.MeasureField)
		if colObj != nil {
			colName = colObj.ColumnName
		}
		if indicator.MeasureType == consts.IndicatorMeasureTypeAgg {
			switch m.AggType {
			case "count":
				selects = append(selects, "COALESCE(COUNT(*),0) AS "+m.ConditionName)
			case "sum":
				selects = append(selects, fmt.Sprintf("COALESCE(SUM(%s),0) AS %s", colName, m.ConditionName))
			case "avg":
				selects = append(selects, fmt.Sprintf("COALESCE(AVG(%s),0) AS %s", colName, m.ConditionName))
			case "max":
				selects = append(selects, fmt.Sprintf("COALESCE(MAX(%s),0) AS %s", colName, m.ConditionName))
			case "min":
				selects = append(selects, fmt.Sprintf("COALESCE(MIN(%s),0) AS %s", colName, m.ConditionName))
			}
			continue
		}
		if indicator.MeasureType == consts.IndicatorMeasureTypeSelect {
			selects = append(selects, colName+" AS "+m.ConditionName)
			continue
		}

	}

	selectClause := "SELECT " + strings.Join(selects, ", ")
	fromClause := "FROM `" + table.Name + "`"

	// 3. 生成WHERE（时间窗口+规则）
	var whereParts = s.genIndicatorTimeWindowSql(indicator)

	// 规则
	var tmpWhereParts []string
	tmpWhereParts, placeholders = s.genIndicatorRuleSql(columnMap, indicator)
	whereParts = append(whereParts, tmpWhereParts...)

	whereClause := ""
	if len(whereParts) > 0 {
		whereClause = "WHERE " + strings.Join(whereParts, " AND ")
	}
	// 拼接SQL
	sql := selectClause + " " + fromClause + " " + whereClause
	return sql, placeholders, nil

}

// Indicator2DorisSql 将指标配置转换为Doris查询语句
func (s *IndicatorService) Indicator2DorisSql(indicator *entity.Indicator) (string, []string, error) {
	// 将指标配置转换为Doris查询语句
	// 1. 获取表名
	var placeholders []string
	var err error
	var table *modelv2.RmsDataSourceTable
	table, err = s.DataSourceTableRepo.First(context.Background(), func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", indicator.TableID)
	})
	if err != nil {
		return "", placeholders, er.Internal.WithMsg("Table ID is invalid")
	}
	var columnMap = make(map[string]string)
	columnMap, err = s.getColumnMapToType(table)
	if err != nil {
		return "", placeholders, er.Internal.WithMsg("column is invalid")
	}
	// 2. 生成SELECT
	selects := make([]string, 0, len(indicator.Measures))
	for _, m := range indicator.Measures {
		colObj := entity.NewDataSourceColumnByColumnID(m.MeasureField)
		colName := string(m.MeasureField)
		if colObj != nil {
			colName = colObj.ColumnName
		}
		if indicator.MeasureType == consts.IndicatorMeasureTypeAgg {
			switch m.AggType {
			case "count":
				selects = append(selects, "COALESCE(COUNT(*),0) AS "+m.ConditionName)
			case "sum":
				selects = append(selects, fmt.Sprintf("COALESCE(SUM(%s),0) AS %s", colName, m.ConditionName))
			case "avg":
				selects = append(selects, fmt.Sprintf("COALESCE(AVG(%s),0) AS %s", colName, m.ConditionName))
			case "max":
				selects = append(selects, fmt.Sprintf("COALESCE(MAX(%s),0) AS %s", colName, m.ConditionName))
			case "min":
				selects = append(selects, fmt.Sprintf("COALESCE(MIN(%s),0) AS %s", colName, m.ConditionName))
			}
			continue
		}
		if indicator.MeasureType == consts.IndicatorMeasureTypeSelect {
			selects = append(selects, colName+" AS "+m.ConditionName)
			continue
		}
	}
	selectClause := "SELECT " + strings.Join(selects, ", ")
	fromClause := "FROM `" + table.Name + "`"

	// 3. 生成WHERE（时间窗口+规则）
	var whereParts = s.genIndicatorTimeWindowSql(indicator)

	// 规则
	var tmpWhereParts []string
	tmpWhereParts, placeholders = s.genIndicatorRuleSql(columnMap, indicator)
	whereParts = append(whereParts, tmpWhereParts...)

	whereClause := ""
	if len(whereParts) > 0 {
		whereClause = "WHERE " + strings.Join(whereParts, " AND ")
	}

	// 拼接SQL
	sql := selectClause + " " + fromClause + " " + whereClause

	return sql, placeholders, nil
}
