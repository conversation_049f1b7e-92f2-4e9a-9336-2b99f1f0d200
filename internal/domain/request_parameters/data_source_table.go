package request_parameters

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type DataSourceColumns struct {
	TableID int64 `form:"table_id" binding:"required" example:"1-abc"` // 数据源ID
}

// CreateDataSourceTable 创建数据源表信息请求参数
type CreateDataSourceTable struct {
	DataSourceID int64  `json:"data_source_id" binding:"required"` // 数据源ID
	Name         string `json:"name" binding:"required"`           // 表名
	Alias        string `json:"alias" binding:"required"`          // 表别名
	Desc         string `json:"desc" binding:"-"`                  // 介绍
	BusinessType int    `json:"business_type" binding:"required"`  // 业务类型 1000=Banking，3000=Acquiring，4000=Issuing
}

// UpdateDataSourceTable 更新数据源表信息请求参数
type UpdateDataSourceTable struct {
	ID   int64  `json:"id" binding:"required"` // 主键ID
	Desc string `json:"desc" binding:"-"`      // 介绍
}

// GetDataSourceTableList 获取数据源表信息列表请求参数
type GetDataSourceTableList struct {
	PageRequest
	DataSourceID int64  `json:"data_source_id" `   // 数据源ID
	Name         string `json:"name" binding:"-"`  // 表名
	Alias        string `json:"alias" binding:"-"` // 表别名
	BusinessType int    `json:"business_type"`     // 业务类型
}

// ValidateBusinessType 验证业务类型
func (r *CreateDataSourceTable) ValidateBusinessType() bool {
	switch r.BusinessType {
	case modelv2.BusinessTypeBanking,
		modelv2.BusinessTypeForeign,
		modelv2.BusinessTypeAcquiring,
		modelv2.BusinessTypeIssuing,
		modelv2.BusinessTypeRamp:
		return true
	default:
		return false
	}
}
