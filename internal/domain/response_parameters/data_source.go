package response_parameters

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/encrypt"
)

// DataSourceListResp 数据源列表响应
type DataSourceListResp struct {
	Total int64             `json:"total"` // 总数
	Items []*DataSourceList `json:"items"` // 数据源列表
}
type DataSourceList struct {
	ID         int64  `json:"id"`          // 主键
	SourceName string `json:"source_name"` // 数据源名称
	SourceType string `json:"source_type"` // 数据源类型
	Address    string `json:"address"`     // 服务器地址
	ConnStatus string `json:"conn_status"` // 连接状态
}

// DataSourceDetail 数据源详情
type DataSourceDetail struct {
	ID            int64             `json:"id"`              // 主键
	SourceName    string            `json:"source_name"`     // 数据源名称
	SourceType    string            `json:"source_type"`     // 数据源类型
	Address       string            `json:"address"`         // 服务器地址
	Port          int               `json:"port"`            // 端口
	Username      string            `json:"username"`        // 用户名
	DatabaseName  string            `json:"database_name"`   // 数据库名称
	Description   *string           `json:"description"`     // 描述
	Password      encrypt.Encrypted `json:"password"`        // 密码
	ConnStatus    string            `json:"conn_status"`     // 连接状态
	LastCheckTime string            `json:"last_check_time"` // 最后检查时间
	FailReason    string            `json:"fail_reason"`
	UpdatedAt     string            `json:"updated_at"` // 更新时间
}

// DataSourceTestResp 测试数据源连接响应
type DataSourceTestResp struct {
	Connected bool   `json:"connected"` // 是否连接成功
	Message   string `json:"message"`   // 连接信息
}
