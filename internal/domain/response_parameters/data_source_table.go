package response_parameters

// DataSourceTableList 数据源表信息列表项
type DataSourceTableList struct {
	ID                  int64    `json:"id"`                   // 主键ID
	DataSourceID        int64    `json:"data_source_id"`       // 数据源ID
	Alias               string   `json:"alias"`                // 表别名
	Name                string   `json:"name"`                 // 表名
	BusinessType        int      `json:"business_type"`        // 业务类型
	BusinessTypeName    string   `json:"business_type_name"`   // 业务类型名称
	AssociatedIndicator []string `json:"associated_indicator"` //关联的指标
}

// DataSourceTableDetail 数据源表信息详情
type DataSourceTableDetail struct {
	ID               int64                  `json:"id"`                 // 主键ID
	DataSourceName   string                 `json:"data_source_name"`   // 数据源名称
	Alias            string                 `json:"alias"`              // 表别名
	Name             string                 `json:"name"`               // 表名
	Desc             string                 `json:"desc"`               // 介绍
	BusinessType     int                    `json:"business_type"`      // 业务类型
	BusinessTypeName string                 `json:"business_type_name"` // 业务类型名称
	Fields           []DataSourceTableField `json:"fields"`             // 字段列表
	LastModifiedBy   string                 `json:"last_modified_by"`   // 最后修改人
	UpdatedAt        string                 `json:"updated_at"`         // 更新时间
}

type DataSourceTableField struct {
	Name       string `json:"name"`        // 字段名称
	ColumnType string `json:"column_type"` // 字段类型
	Desc       string `json:"desc"`        // 字段描述
}
