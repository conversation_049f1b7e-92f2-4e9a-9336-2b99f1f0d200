// internal/data/datav2/rms_data_source.go
package datav2

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/encrypt"

	_ "github.com/go-sql-driver/mysql" // 导入MySQL驱动
	"gorm.io/gorm/logger"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// RmsDataSource 数据源配置
type RmsDataSource struct {
	Empty
}

// TableName RmsDataSource's table name
func (*RmsDataSource) TableName() string {
	return modelv2.TableNameRmsDataSource
}

// NewRmsDataSource creates a new RmsDataSource instance
func NewRmsDataSource() repo.DataSourceRepo {
	return &RmsDataSource{}
}

// Create creates a new RmsDataSource record
func (r *RmsDataSource) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsDataSource) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsDataSource{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return er.ConvertDBError(err)
}

// Update updates an existing modelv2.RmsDataSource record
func (r *RmsDataSource) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsDataSource) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsDataSource{})).Updates(model).Error)
	})
}

// Updates updates specific fields in an existing modelv2.RmsDataSource record
func (r *RmsDataSource) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsDataSource{})).Updates(data).Error)
	})
}

// Delete deletes a modelv2.RmsDataSource record
func (r *RmsDataSource) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsDataSource{})).Delete(&modelv2.RmsDataSource{}).Error)
	})
}

// List retrieves a list of modelv2.RmsDataSource records with optional total count
// withTotal: true returns total count, false returns only list data
func (r *RmsDataSource) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsDataSource, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsDataSource{})).WithContext(ctx))
	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

// Count counts modelv2.RmsDataSource records based on the condition
func (r *RmsDataSource) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsDataSource{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

// First retrieves a single modelv2.RmsDataSource record
func (r *RmsDataSource) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsDataSource, error) {
	var item modelv2.RmsDataSource
	var err error
	err = fn(global.DB.Model(&modelv2.RmsDataSource{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

// TestConnection 测试数据源连接
func (r *RmsDataSource) TestConnection(ctx context.Context, dataSource *modelv2.RmsDataSource) (bool, error) {
	switch dataSource.SourceType {
	case "mysql", "doris":
		// 创建带2秒超时的上下文
		ctx, cancel := context.WithTimeout(ctx, 2*time.Second)

		defer cancel()

		// 构建DSN
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&timeout=2s&readTimeout=2s&writeTimeout=2s&net_write_timeout=2&net_read_timeout=2",
			dataSource.Username,
			encrypt.Decrypt(dataSource.Password),
			dataSource.Address,
			dataSource.Port,
			dataSource.DatabaseName)

		// 直接使用database/sql打开连接
		db, err := sql.Open("mysql", dsn)

		if err != nil {
			return false, er.Internal.WithMsg("创建数据库连接失败").WithErr(err)
		}
		defer db.Close()

		// 设置连接池参数
		db.SetConnMaxLifetime(2 * time.Second)
		db.SetMaxOpenConns(1)
		db.SetMaxIdleConns(0)

		// 直接使用PingContext测试连接
		err = db.PingContext(ctx)
		if err != nil {
			return false, er.Internal.WithMsg("数据库连接测试失败").WithErr(err)
		}

		return true, nil
	default:
		return false, er.InvalidArgument.WithMsg(fmt.Sprintf("不支持的数据源类型: %s", dataSource.SourceType))
	}
}

func (r *RmsDataSource) GetConnection(dataSource *modelv2.RmsDataSource) (*gorm.DB, error) {
	switch dataSource.SourceType {
	case "mysql", "doris":
		var do bool
		var err error
		do, err = r.TestConnection(context.Background(), dataSource)
		if err != nil {
			return nil, err
		}
		if do {
			// 在DSN字符串中添加更严格的超时参数和禁用重试
			dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=5s&readTimeout=5s&writeTimeout=5s",
				dataSource.Username,
				encrypt.Decrypt(dataSource.Password),
				dataSource.Address,
				dataSource.Port,
				dataSource.DatabaseName)

			// 使用带超时控制的配置打开数据库连接
			db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
				QueryFields: true,
				//Logger: logger.New(
				//	log.New(io.Discard, "", 0), // 禁用SQL日志输出
				//	logger.Config{
				//		SlowThreshold: 5 * time.Second,
				//		LogLevel:      logger.Silent, // 完全禁用日志
				//	},
				//),
				Logger: logger.New(
					log.New(os.Stdout, "\r\n", log.LstdFlags), // 输出到标准输出
					logger.Config{
						LogLevel: logger.Info, // 设置日志级别
						Colorful: false,       // 是否使用颜色
					},
				),
				SkipDefaultTransaction: true,  // 跳过默认事务提升性能
				PrepareStmt:            false, // 不使用预处理语句
			})
			if err != nil {
				return nil, er.Internal.WithMsg("连接数据库失败").WithErr(err).WithStack()
			}

			// 设置更严格的连接池参数
			sqlDB, err := db.DB()
			if err != nil {
				return nil, er.Internal.WithMsg("获取数据库实例失败").WithErr(err).WithStack()
			}

			sqlDB.SetConnMaxLifetime(5 * time.Second)
			sqlDB.SetConnMaxIdleTime(5 * time.Second)
			sqlDB.SetMaxOpenConns(10)
			sqlDB.SetMaxIdleConns(0) // 不保留空闲连接

			return db, nil
		}
		return nil, er.Internal.WithMsg("Database connection exception")

	default:
		return nil, er.InvalidArgument.WithMsg(fmt.Sprintf("不支持的数据源类型: %s", dataSource.SourceType))
	}
}
