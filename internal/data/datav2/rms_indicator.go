package datav2

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/compare"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsIndicator 指标配置
type RmsIndicator struct {
	Empty
}

// TableName 表名
func (r *RmsIndicator) TableName() string {
	return modelv2.TableNameRmsIndicator
}

// NewRmsIndicator 创建指标配置实例
func NewRmsIndicator() repo.IndicatorRepo {
	return &RmsIndicator{}
}

// Create 创建指标配置
func (r *RmsIndicator) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicator) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicator{}).WithContext(ctx).Create(data).Error)
	})
}

// Update 更新指标配置
func (r *RmsIndicator) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicator) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicator{})).Updates(data).Error)
	})
}

// Updates 更新指标配置版本
func (r *RmsIndicator) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicator{})).Updates(data).Error)
	})
}

// First 获取单个指标配置
func (r *RmsIndicator) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicator, error) {
	var item modelv2.RmsIndicator
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicator{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// List 获取指标配置列表
func (r *RmsIndicator) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicator, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsIndicator{}).WithContext(ctx)))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return total, items, err
	}

	return 0, items, nil
}

// Delete 删除指标配置
func (r *RmsIndicator) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicator{})).Delete(&modelv2.RmsIndicator{}).Error)
	})
}

// RmsIndicatorVersion 指标配置版本
type RmsIndicatorVersion struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorVersion) TableName() string {
	return modelv2.TableNameRmsIndicatorVersion
}

// NewRmsIndicatorVersion 创建指标配置版本实例
func NewRmsIndicatorVersion() repo.IndicatorVersionRepo {
	return &RmsIndicatorVersion{}
}

// Create 创建指标配置版本
func (r *RmsIndicatorVersion) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicatorVersion) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx).Create(data).Error)
	})
}

// Updates 更新指标配置版本
func (r *RmsIndicatorVersion) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersion{})).Updates(data).Error)
	})
}

// NewVersion 创建新的版本号
func (r *RmsIndicatorVersion) NewVersion(ctx context.Context, indicatorID int64) string {
	count, err := r.Count(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("indicator_id = ?", indicatorID).Where("version > ?", "0")
	})
	if err != nil {
		return ""
	}

	return modelv2.GetIndicatorVersion(1, count+1)
}

// First 获取单个指标配置版本
func (r *RmsIndicatorVersion) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicatorVersion, error) {
	var item modelv2.RmsIndicatorVersion
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// List 获取指标配置版本列表
func (r *RmsIndicatorVersion) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicatorVersion, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx)))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return total, items, err
	}

	return 0, items, nil
}

// Count 获取指标配置版本数量
func (r *RmsIndicatorVersion) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (int64, error) {
	var total int64
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx)).Count(&total).Error)
	return total, err
}

// RmsIndicatorRule 指标配置规则
type RmsIndicatorRule struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorRule) TableName() string {
	return modelv2.TableNameRmsIndicatorRule
}

// NewRmsIndicatorRule 创建指标配置规则实例
func NewRmsIndicatorRule() repo.IndicatorRuleRepo {
	return &RmsIndicatorRule{}
}

// BatchCreate 批量创建指标配置规则
func (r *RmsIndicatorRule) BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorRule) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorRule{}).WithContext(ctx).Create(data).Error)
	})
}

// List 获取指标配置规则列表
func (r *RmsIndicatorRule) List(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) ([]*modelv2.RmsIndicatorRule, error) {
	var items []*modelv2.RmsIndicatorRule
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorRule{}).WithContext(ctx)).Find(&items).Error)
	return items, err
}

// Update 更新指标配置
func (r *RmsIndicatorRule) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorRule) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorRule{})).Updates(data).Error)
	})
}

// Delete 删除指标配置规则
func (r *RmsIndicatorRule) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorRule{})).Delete(&modelv2.RmsIndicatorRule{}).Error)
	})
}

// RmsIndicatorMeasure 指标配置度量
type RmsIndicatorMeasure struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorMeasure) TableName() string {
	return modelv2.TableNameRmsIndicatorMeasure
}

// NewRmsIndicatorMeasure 创建指标配置度量实例
func NewRmsIndicatorMeasure() repo.IndicatorMeasureRepo {
	return &RmsIndicatorMeasure{}
}

// BatchCreate 批量创建指标配置度量
func (r *RmsIndicatorMeasure) BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorMeasure) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorMeasure{}).WithContext(ctx).Create(data).Error)
	})
}

// Update 更新指标配置
func (r *RmsIndicatorMeasure) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorMeasure) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorMeasure{})).Updates(data).Error)
	})
}

// List 获取指标配置度量列表
func (r *RmsIndicatorMeasure) List(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) ([]*modelv2.RmsIndicatorMeasure, error) {
	var items []*modelv2.RmsIndicatorMeasure
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorMeasure{}).WithContext(ctx)).Find(&items).Error)
	return items, err
}

// Delete 删除指标配置度量
func (r *RmsIndicatorMeasure) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorMeasure{})).Delete(&modelv2.RmsIndicatorMeasure{}).Error)
	})
}

// RmsIndicatorVersionHistory 指标配置版本历史
type RmsIndicatorVersionHistory struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorVersionHistory) TableName() string {
	return modelv2.TableNameRmsIndicatorVersionHistory
}

type IndicatorCompare struct {
	BaseInfo *modelv2.RmsIndicatorVersion
	Measure  []*modelv2.RmsIndicatorMeasure
	Rules    []*modelv2.RmsIndicatorRule
}

// NewRmsIndicatorVersionHistory 创建指标配置版本历史实例
func NewRmsIndicatorVersionHistory() repo.IndicatorVersionHistoryRepo {
	return &RmsIndicatorVersionHistory{}
}

// Create 创建指标配置版本历史
func (r *RmsIndicatorVersionHistory) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicatorVersionHistory) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx).Create(data).Error)
	})
}

// BatchCreate 批量创建指标配置版本历史
func (r *RmsIndicatorVersionHistory) BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorVersionHistory) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx).Create(data).Error)
	})
}

// Update 更新指标配置版本历史
func (r *RmsIndicatorVersionHistory) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorVersionHistory) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersionHistory{})).Updates(data).Error)
	})
}

// Updates 更新指标配置版本历史
func (r *RmsIndicatorVersionHistory) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersionHistory{})).Updates(data).Error)
	})
}

// First 获取单个指标配置版本历史
func (r *RmsIndicatorVersionHistory) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicatorVersionHistory, error) {
	var item modelv2.RmsIndicatorVersionHistory
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// List 获取指标配置版本历史列表
func (r *RmsIndicatorVersionHistory) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicatorVersionHistory, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx)))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return total, items, err
	}

	return 0, items, nil
}

// Delete 删除指标配置版本历史
func (r *RmsIndicatorVersionHistory) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersionHistory{})).Delete(&modelv2.RmsIndicatorVersionHistory{}).Error)
	})
}

func (r *RmsIndicatorVersionHistory) AnalyzeVersionAndSave(ctx context.Context, isScript bool, modifyUser string, oldVersion *modelv2.RmsIndicatorVersion, newVersion *modelv2.RmsIndicatorVersion) (err error) {
	var history *modelv2.RmsIndicatorVersionHistory
	var isDraft bool
	if newVersion.Version == "" {
		isDraft = true
	}
	history, err = r.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("version_id = ?", newVersion.ID).Where("indicator_id = ?", newVersion.IndicatorID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		history = &modelv2.RmsIndicatorVersionHistory{
			IndicatorID: newVersion.IndicatorID,
			VersionID:   newVersion.ID,
			Version:     newVersion.Version,
			IsDraft:     isDraft,
		}
	}
	history.LastModified = modifyUser
	if oldVersion == nil {
		history.Desc = "Initial version created"
		if history.ID > 0 {
			err = r.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", history.ID)
			}, history)
		} else {
			err = r.Create(ctx, nil, history)
		}
		return
	}
	if isScript {
		var desc string
		if oldVersion.Script != newVersion.Script {
			desc += "Script was changed\n"
		}
		if oldVersion.Remark != newVersion.Remark {
			desc += "Remark was changed"
		}
		history.Desc = desc
	} else {
		var oldIndicatorCompare, newIndicatorCompare IndicatorCompare
		oldIndicatorCompare.BaseInfo = oldVersion
		newIndicatorCompare.BaseInfo = newVersion
		{
			oldIndicatorCompare.Measure, err = NewRmsIndicatorMeasure().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", oldVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
			newIndicatorCompare.Measure, err = NewRmsIndicatorMeasure().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", newVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
			oldIndicatorCompare.Rules, err = NewRmsIndicatorRule().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", oldVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
			newIndicatorCompare.Rules, err = NewRmsIndicatorRule().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", newVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
		}
		var tmpDesc []string
		if oldIndicatorCompare.BaseInfo.TimeWindowType != newIndicatorCompare.BaseInfo.TimeWindowType ||
			oldIndicatorCompare.BaseInfo.TimeWindowUnit != newIndicatorCompare.BaseInfo.TimeWindowUnit ||
			oldIndicatorCompare.BaseInfo.TimeWindowValue != newIndicatorCompare.BaseInfo.TimeWindowValue ||
			oldIndicatorCompare.BaseInfo.TimeWindowExcluding != newIndicatorCompare.BaseInfo.TimeWindowExcluding ||
			oldIndicatorCompare.BaseInfo.StartTime != newIndicatorCompare.BaseInfo.StartTime ||
			oldIndicatorCompare.BaseInfo.EndTime != newIndicatorCompare.BaseInfo.EndTime {
			timeWindow := func(t *modelv2.RmsIndicatorVersion) (res string) {
				switch t.TimeWindowType {
				case consts.IndicatorMeasureWindowTypeSliding:
					res = fmt.Sprintf("Last %d %s", t.TimeWindowValue, consts.IndicatorVersionSlidingTimeWindowUnits[t.TimeWindowUnit])
					if t.TimeWindowExcluding {
						res = fmt.Sprintf("Last %d %s excluding this %s", t.TimeWindowValue, consts.IndicatorVersionSlidingTimeWindowUnits[t.TimeWindowUnit], consts.IndicatorVersionSlidingTimeWindowUnits[t.TimeWindowUnit])
					}
					return
				case consts.IndicatorMeasureWindowTypeFixed:
					return fmt.Sprintf("`%s`", consts.IndicatorVersionFixedTimeWindowUnits[t.TimeWindowUnit])
				case consts.IndicatorMeasureWindowTypeBefore:
					return fmt.Sprintf("Before %s", t.StartTime)
				case consts.IndicatorMeasureWindowTypeAfter:
					return fmt.Sprintf("After %s", t.EndTime)
				case consts.IndicatorMeasureWindowTypeBetween:
					return fmt.Sprintf("Between %s and %s", t.StartTime, t.EndTime)
				}
				return
			}
			tmpDesc = append(tmpDesc, fmt.Sprintf("The time window was changed from %s to %s", timeWindow(oldIndicatorCompare.BaseInfo), timeWindow(newIndicatorCompare.BaseInfo)))
		}
		if string(oldIndicatorCompare.BaseInfo.TimeWindowColumnID) != string(newIndicatorCompare.BaseInfo.TimeWindowColumnID) {
			oldColumn := entity.NewDataSourceColumnByColumnID(oldIndicatorCompare.BaseInfo.TimeWindowColumnID)
			newColumn := entity.NewDataSourceColumnByColumnID(newIndicatorCompare.BaseInfo.TimeWindowColumnID)
			if oldColumn != nil && newColumn != nil {
				tmpDesc = append(tmpDesc, fmt.Sprintf("The time window column was changed from %s to %s", oldColumn.ColumnName, newColumn.ColumnName))
			}
		}
		if string(oldIndicatorCompare.BaseInfo.TimeWindowColumnID) != string(newIndicatorCompare.BaseInfo.TimeWindowColumnID) {
			oldColumn := entity.NewDataSourceColumnByColumnID(oldIndicatorCompare.BaseInfo.TimeWindowColumnID)
			newColumn := entity.NewDataSourceColumnByColumnID(newIndicatorCompare.BaseInfo.TimeWindowColumnID)
			if oldColumn != nil && newColumn != nil {
				tmpDesc = append(tmpDesc, fmt.Sprintf("The time window column was changed from %s to %s", oldColumn.ColumnName, newColumn.ColumnName))
			}
		}

		history.Desc = strings.Join(tmpDesc, "\n") + "\n"
		// 处理 Measures
		tmpDesc = []string{}

		var oldMeasureMap = make(map[int64]*modelv2.RmsIndicatorMeasure)
		for _, oldMeasure := range oldIndicatorCompare.Measure {
			oldMeasureMap[oldMeasure.ID] = oldMeasure
		}
		var insMeasure, _, delMeasure = compare.NewCompare[int64, *modelv2.RmsIndicatorMeasure](func(data *modelv2.RmsIndicatorMeasure) int64 {
			return data.ParentID
		}, func(new *modelv2.RmsIndicatorMeasure, old *modelv2.RmsIndicatorMeasure) bool {
			var up bool
			var prefix string
			if new.ConditionName != old.ConditionName {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("The condition name was changed from %s to %s", old.ConditionName, new.ConditionName))
			} else {
				prefix = "Record with the condition name of " + old.ConditionName
			}
			if new.AggType != old.AggType {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s measure agg type changed from %s to %s", prefix, old.AggType, new.AggType))
				prefix = ""
			}
			if new.MeasureField != old.MeasureField {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s measure field changed from %s to %s", prefix, old.MeasureField, new.MeasureField))
				prefix = ""
			}
			history.Desc += strings.Join(tmpDesc, ",") + "\n"
			return up
		}, oldMeasureMap).Compare(newIndicatorCompare.Measure)
		for _, ins := range insMeasure {
			history.Desc += fmt.Sprintf("New measure %s was created", ins.ConditionName) + "\n"
		}
		for _, del := range delMeasure {
			history.Desc += fmt.Sprintf("Measure %s was deleted", del.ConditionName) + "\n"
		}

		//处理规则
		tmpDesc = []string{}

		var oldRuleMap = make(map[int64]*modelv2.RmsIndicatorRule)
		for _, oldRule := range oldIndicatorCompare.Rules {
			oldRuleMap[oldRule.ID] = oldRule
		}
		var insRule, _, delRule = compare.NewCompare[int64, *modelv2.RmsIndicatorRule](func(data *modelv2.RmsIndicatorRule) int64 {
			return data.ParentID
		}, func(new *modelv2.RmsIndicatorRule, old *modelv2.RmsIndicatorRule) bool {
			var up bool
			var prefix string
			oldColumn := entity.NewDataSourceColumnByColumnID(old.ColumnID)
			newColumn := entity.NewDataSourceColumnByColumnID(new.ColumnID)
			if oldColumn == nil || newColumn == nil {
				return false
			}
			if new.ColumnID != old.ColumnID {
				up = true

				tmpDesc = append(tmpDesc, fmt.Sprintf("Rule: The column was changed from %s to %s", oldColumn.ColumnName, newColumn.ColumnName))
			} else {
				prefix = "Rule: Record with the column of " + newColumn.ColumnName
			}
			if new.Operator != old.Operator {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s operator changed from %s to %s", prefix, old.Operator, new.Operator))
				prefix = ""
			}
			if new.Preview != old.Preview {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s preview changed from %s to %s", prefix, old.Preview, new.Preview))
				prefix = ""
			}
			if new.HasLeftBrackets != old.HasLeftBrackets {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s has left brackets changed from %t to %t", prefix, old.HasLeftBrackets, new.HasLeftBrackets))
				prefix = ""
			}
			if new.HasRightBrackets != old.HasRightBrackets {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s has right brackets changed from %t to %t", prefix, old.HasRightBrackets, new.HasRightBrackets))
				prefix = ""
			}
			if new.Connector != old.Connector {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s connector changed from %s to %s", prefix, old.Connector, new.Connector))
				prefix = ""
			}
			if new.Value != old.Value {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s value changed from %s to %s", prefix, old.Value, new.Value))
				prefix = ""
			}
			if new.ValueType != old.ValueType {
				up = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("%s value type changed from %s to %s", prefix, old.ValueType, new.ValueType))
				prefix = ""
			}

			history.Desc += strings.Join(tmpDesc, ",") + "\n"
			return up
		}, oldRuleMap).Compare(newIndicatorCompare.Rules)
		for _, ins := range insRule {
			newColumn := entity.NewDataSourceColumnByColumnID(ins.ColumnID)
			if newColumn != nil {
				history.Desc += fmt.Sprintf("Rule: New rule %s was created", newColumn.ColumnName) + "\n"

			}
		}
		for _, del := range delRule {
			newColumn := entity.NewDataSourceColumnByColumnID(del.ColumnID)
			if newColumn != nil {
				history.Desc += fmt.Sprintf("Rule：%s was deleted", newColumn.ColumnName) + "\n"
			}
		}
	}

	if history.ID > 0 {
		err = r.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", history.ID)
		}, history)
	} else {
		err = r.Create(ctx, nil, history)
	}
	return
}
