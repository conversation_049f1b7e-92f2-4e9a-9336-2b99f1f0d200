package modelv2

import (
	"fmt"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/compare"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
)

// 表名常量
const (
	TableNameRmsIndicator               = "rms_indicator"                 // 指标配置表
	TableNameRmsIndicatorVersion        = "rms_indicator_version"         // 指标配置版本表
	TableNameRmsIndicatorVersionHistory = "rms_indicator_version_history" // 指标配置版本历史表
	TableNameRmsIndicatorRule           = "rms_indicator_rule"            // 指标配置规则表
	TableNameRmsIndicatorMeasure        = "rms_indicator_measure"         // 指标配置度量表

	RedisCacheVisualIndicator = "risk:{indicator}:visual"
	RedisCacheScriptIndicator = "risk:{indicator}:script"
)
const (
	IndicatorStatusPublished = "published"
	IndicatorStatusDisable   = "disable"
	IndicatorStatusDraft     = "draft"
)

// RmsIndicator 指标配置表
type RmsIndicator struct {
	ID               int64  `json:"id" gorm:"primaryKey;column:id"`                                              // 主键ID
	IndicatorName    string `json:"indicator_name" gorm:"column:indicator_name;comment:指标名称"`                // 指标名称
	IsScript         bool   `json:"is_script" gorm:"column:is_script;default:0"`                                 // 是否为脚本指标
	AccessPoint      string `json:"access_point" gorm:"column:access_point"`                                     // 访问点列表
	MeasureType      string `json:"measure_type" gorm:"column:measure_type"`                                     // 度量类型
	TableID          int64  `json:"table_id" gorm:"column:table_id;index"`                                       // 关联的表ID
	CurrentVersionID int64  `json:"current_version_id" gorm:"column:current_version_id;index"`                   // 当前使用的版本ID
	CurrentVersion   string `json:"current_version" gorm:"column:current_version;default:'';index"`              // 当前使用的版本,例如1.1
	Status           string `json:"status" gorm:"column:status;default:'draft';comment:published,disable,draft"` // published,disable,draft
	DraftVersionID   int64  `json:"draft_version_id" gorm:"column:draft_version_id;index"`                       // 草稿版本ID
	BaseDeletedModel
}

// TableName 表名
func (r *RmsIndicator) TableName() string {
	return TableNameRmsIndicator
}

// 时间窗口单位,当type为1时，1:Minutes/2:Hours/3:Days/4:Weeks/5:Months

// RmsIndicatorVersion 指标配置版本表
type RmsIndicatorVersion struct {
	ID                  int64                     `json:"id" gorm:"primaryKey;column:id"`                // 主键ID
	IndicatorID         int64                     `json:"indicator_id" gorm:"column:indicator_id;index"` // 关联的指标ID
	Script              string                    `json:"script" gorm:"column:script;type:text"`
	TimeWindowType      int                       `json:"time_window_type" gorm:"column:time_window_type"` // 时间窗口类型 1.Sliding Window 2.Fixed Window 3.Before 4.After 5.Between
	StartTime           string                    `json:"start_time" gorm:"column:start_time"`
	EndTime             string                    `json:"end_time" gorm:"column:end_time"`
	TimeWindowValue     int                       `json:"time_window_value" gorm:"column:time_window_value"`         // 时间窗口值
	TimeWindowUnit      int                       `json:"time_window_unit" gorm:"column:time_window_unit"`           // 时间窗口单位
	TimeWindowColumnID  entity.DataSourceColumnID `json:"time_window_column_id" gorm:"column:time_window_column_id"` // 时间窗口字段
	TimeWindowExcluding bool                      `json:"time_window_excluding" gorm:"column:time_window_excluding"` // 是否排除
	Remark              string                    `json:"remark" gorm:"column:remark"`                               // 备注
	Version             string                    `json:"version" gorm:"column:version;index;default:''"`            // 版本,例如1.1
	BaseModel
}

func GetIndicatorVersion(masterVersion int64, subVersion int64) string {
	return fmt.Sprintf("%d.%d", masterVersion, subVersion)
}

// TableName 表名
func (r *RmsIndicatorVersion) TableName() string {
	return TableNameRmsIndicatorVersion
}

// RmsIndicatorVersionHistory 指标配置版本历史表
type RmsIndicatorVersionHistory struct {
	ID           int64  `json:"id" gorm:"primaryKey;column:id"`                 // 主键ID
	IndicatorID  int64  `json:"indicator_id" gorm:"column:indicator_id;index"`  // 指标ID
	VersionID    int64  `json:"version_id" gorm:"column:version_id;index"`      // 版本ID
	Version      string `json:"version" gorm:"column:version;index;default:''"` // 版本号,例如1.1
	Desc         string `json:"desc" gorm:"column:desc;type:text"`              // 描述
	IsDraft      bool   `json:"is_draft" gorm:"column:is_draft;default:0"`      // 是否为草稿 1:草稿 0:正式版本
	LastModified string `json:"last_modified" gorm:"column:last_modified;default:''"`
	BaseModel
}

// TableName 表名
func (r *RmsIndicatorVersionHistory) TableName() string {
	return TableNameRmsIndicatorVersionHistory
}

// RmsIndicatorMeasure 指标配置度量表
type RmsIndicatorMeasure struct {
	ID            int64                     `json:"id" gorm:"primaryKey;column:id"`                   // 主键ID
	VersionID     int64                     `json:"version_id" gorm:"column:version_id;index"`        // 版本ID
	AggType       string                    `json:"agg_type" gorm:"column:agg_type"`                  // 度量类型
	MeasureField  entity.DataSourceColumnID `json:"measure_field" gorm:"column:measure_field"`        // 度量字段
	ConditionName string                    `json:"condition_name" gorm:"column:condition_name"`      // 条件名称
	ParentID      int64                     `json:"parent_id" gorm:"column:parent_id;comment:来源ID"` // 记录如果创建来自其他记录更新，那就填上原来记录ID
}

// TableName 表名
func (r *RmsIndicatorMeasure) TableName() string {
	return TableNameRmsIndicatorMeasure
}
func NewRmsIndicatorMeasure() *RmsIndicatorMeasure {
	return &RmsIndicatorMeasure{}
}
func (r *RmsIndicatorMeasure) DiffUp(oldMeasures, newMeasures []*RmsIndicatorMeasure) (insMeasures, upMeasures []*RmsIndicatorMeasure, delMeasureIds []int64) {
	var delMeasures []*RmsIndicatorMeasure
	var measuresMap = make(map[int64]*RmsIndicatorMeasure)
	for _, measure := range oldMeasures {
		measuresMap[measure.ID] = measure
	}
	var tmpInsMeasures []*RmsIndicatorMeasure
	tmpInsMeasures, upMeasures, delMeasures = compare.NewCompare[int64, *RmsIndicatorMeasure](func(measure *RmsIndicatorMeasure) int64 {
		return measure.ID
	}, func(measure *RmsIndicatorMeasure, measure2 *RmsIndicatorMeasure) bool {
		if measure.VersionID != measure2.VersionID || measure.AggType != measure2.AggType || measure.MeasureField != measure2.MeasureField || measure.ConditionName != measure2.ConditionName {
			return true
		}
		return false
	}, measuresMap).Compare(newMeasures)
	for _, measure := range delMeasures {
		delMeasureIds = append(delMeasureIds, measure.ID)
	}
	for _, measure := range tmpInsMeasures {
		measure.ID = 0
		insMeasures = append(insMeasures, measure)
	}
	return
}

// RmsIndicatorRule 指标配置规则表
type RmsIndicatorRule struct {
	ID               int64                     `json:"id" gorm:"primaryKey;column:id"`                      // 主键ID
	VersionID        int64                     `json:"version_id" gorm:"column:version_id;index"`           // 版本ID
	Preview          string                    `json:"preview" gorm:"column:preview"`                       // 预览
	HasLeftBrackets  bool                      `json:"has_left_brackets" gorm:"column:has_left_brackets"`   // 是否包含左括号
	HasRightBrackets bool                      `json:"has_right_brackets" gorm:"column:has_right_brackets"` // 是否包含右括号
	Connector        string                    `json:"connector" gorm:"column:connector;comment:and, or"`   // 连接符(and, or)
	ColumnID         entity.DataSourceColumnID `json:"column_id" gorm:"column:column_id"`                   // 列名
	Operator         string                    `json:"operator" gorm:"column:operator"`                     // 操作符
	Value            string                    `json:"value" gorm:"column:value"`                           // 比较值的字段名或具体值
	ValueType        string                    `json:"value_type" gorm:"column:value_type"`                 // 比较值类型(fixed: 固定值, field: 字段比较)
	ParentID         int64                     `json:"parent_id" gorm:"column:parent_id;comment:来源ID"`    // 记录如果创建来自其他记录更新，那就填上原来记录ID

}

// TableName 表名
func (r *RmsIndicatorRule) TableName() string {
	return TableNameRmsIndicatorRule
}
func NewRmsIndicatorRule() *RmsIndicatorRule {
	return &RmsIndicatorRule{}
}
func (r *RmsIndicatorRule) DiffUp(oldRules, newRules []*RmsIndicatorRule) (insRules, upRules []*RmsIndicatorRule, delRuleIds []int64) {
	var delRules []*RmsIndicatorRule
	var rulesMap = make(map[int64]*RmsIndicatorRule)
	for _, rule := range oldRules {
		rulesMap[rule.ID] = rule
	}
	var tmpInsRules []*RmsIndicatorRule
	tmpInsRules, upRules, delRules = compare.NewCompare[int64, *RmsIndicatorRule](func(measure *RmsIndicatorRule) int64 {
		return measure.ID
	}, func(measure *RmsIndicatorRule, measure2 *RmsIndicatorRule) bool {
		if measure.VersionID != measure2.VersionID ||
			measure.Preview != measure2.Preview ||
			measure.HasLeftBrackets != measure2.HasLeftBrackets ||
			measure.HasRightBrackets != measure2.HasRightBrackets ||
			measure.Connector != measure2.Connector ||
			string(measure.ColumnID) != string(measure2.ColumnID) ||
			measure.Operator != measure2.Operator ||
			measure.Value != measure2.Value ||
			measure.ValueType != measure2.ValueType {
			return true
		}
		return false
	}, rulesMap).Compare(newRules)
	for _, rule := range delRules {
		delRuleIds = append(delRuleIds, rule.ID)
	}
	for _, rule := range tmpInsRules {
		rule.ID = 0
		insRules = append(insRules, rule)
	}
	return
}

// 操作符映射
var IndicatorSQLOperatorMap = map[string]string{
	"eq":       "=",
	"gt":       ">",
	"lt":       "<",
	"gte":      ">=",
	"lte":      "<=",
	"ne":       "!=",
	"in":       "IN",
	"notin":    "NOT IN",
	"is_true":  "IS TRUE",
	"is_false": "IS FALSE",
}
